# 获取脚本当前所在的目录
$scriptPath = $PSScriptRoot

# 定义任务名称
$taskName = "重启我的SRA服务"

# 定义要执行的批处理文件路径
$batFilePath = Join-Path $scriptPath "restart_services.bat"

# 定义任务触发器（系统启动时）
$trigger = New-ScheduledTaskTrigger -AtStartup

# 定义任务操作（运行批处理文件）
# -WorkingDirectory 指定了脚本的执行目录，这非常关键
$action = New-ScheduledTaskAction -Execute $batFilePath -WorkingDirectory $scriptPath

# 定义任务的设置
# -RunLevel Highest 确保任务以最高权限运行
$settings = New-ScheduledTaskSettingsSet -RunLevel Highest

# 注册任务计划
# -Force 参数会覆盖同名的现有任务
Register-ScheduledTask -TaskName $taskName -Trigger $trigger -Action $action -Settings $settings -Force

Write-Host "成功创建开机自启动任务: $taskName"
Write-Host "任务将在下次系统启动时自动运行。"
Read-Host "按 Enter 键退出..."

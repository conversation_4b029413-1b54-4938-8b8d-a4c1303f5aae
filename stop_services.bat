@echo off
chcp 65001 > nul
echo "正在停止服务..."

REM 查找并终止 FastAPI 进程
wmic process where "name='python.exe' and commandline like '%%src/fast_api.py%%'" get processid /value | find "ProcessId" > .pids.tmp
FOR /F "tokens=2 delims==" %%I IN (.pids.tmp) DO taskkill /F /PID %%I

REM 查找并终止 Celery Worker 进程
wmic process where "name='python.exe' and commandline like '%%celery_worker%%'" get processid /value | find "ProcessId" >> .pids.tmp
FOR /F "tokens=2 delims==" %%I IN (.pids.tmp) DO taskkill /F /PID %%I

REM 清理临时文件
del .pids.tmp

echo "服务已停止。"

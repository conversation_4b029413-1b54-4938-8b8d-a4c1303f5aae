# SRA系统 Mermaid流程图索引

本文档提供了SRA智能招聘助手系统的所有Mermaid流程图的索引和说明。这些流程图从不同角度展示了系统的架构、业务流程、数据流向和组件交互关系。

## 流程图列表

### 1. 系统整体架构图
**用途**: 展示SRA系统的整体架构和各层次组件关系
**内容**: 
- 外部系统交互（用户、SRAA服务、Boss直聘）
- API层（FastAPI、各种API接口、结果管理器）
- 业务流程层（登录、抓取、构建、筛选、回调）
- 核心层（浏览器管理、任务处理、Worker进程）
- 工具层（日志、追踪、清理、重试）
- 基础设施（Redis、文件系统、配置）

**关键特点**:
- 清晰的分层架构
- 组件间的依赖关系
- 外部系统集成点
- 数据流向指示
```mermaid
graph TB
    subgraph "外部系统"
        EXT[外部SRAA服务]
        USER[用户/管理员]
        BOSS[Boss直聘网站]
    end

    subgraph "SRA系统"
        subgraph "API层"
            FASTAPI[FastAPI Web服务]
            AGENT_API[Agent API<br/>业务接口]
            CTRL_API[Control API<br/>控制接口]
            DISPATCH_API[Dispatch API<br/>任务分发]
            RESULT_MGR[Result Manager<br/>结果管理器]
        end

        subgraph "业务流程层"
            LOGIN[Login Flow<br/>登录流程]
            FETCH[Geek Fetch Flow<br/>候选人抓取]
            BUILD[Geek Info Build<br/>信息构建]
            FILTER[Geek Filter<br/>智能筛选]
            CALLBACK[Callback<br/>回调处理]
        end

        subgraph "核心层"
            BROWSER[Browser Manager<br/>浏览器管理]
            TASK_PROC[Task Processor<br/>任务处理器]
            WORKER[Celery Worker<br/>工作进程]
        end

        subgraph "工具层"
            LOGGER[Logger<br/>日志管理]
            TRACING[Tracing Manager<br/>追踪管理]
            CLEANUP[Auto Cleanup<br/>自动清理]
            RETRY[Retry Handler<br/>重试处理]
        end

        subgraph "基础设施"
            REDIS[(Redis<br/>任务队列/状态存储)]
            FILES[文件系统<br/>日志/数据/trace]
            CONFIG[配置管理]
        end
    end

    %% 外部交互
    USER --> FASTAPI
    FASTAPI --> EXT
    WORKER --> BOSS

    %% API层内部
    FASTAPI --> AGENT_API
    FASTAPI --> CTRL_API
    FASTAPI --> DISPATCH_API
    AGENT_API --> RESULT_MGR
    DISPATCH_API --> RESULT_MGR

    %% API到队列
    AGENT_API --> REDIS
    CTRL_API --> REDIS
    DISPATCH_API --> REDIS

    %% Worker到业务流程
    WORKER --> LOGIN
    WORKER --> FETCH
    WORKER --> BUILD
    WORKER --> FILTER
    WORKER --> CALLBACK

    %% 业务流程内部
    LOGIN --> FETCH
    FETCH --> BUILD
    BUILD --> FILTER
    FILTER --> CALLBACK

    %% 核心层
    WORKER --> TASK_PROC
    TASK_PROC --> BROWSER
    REDIS --> WORKER

    %% 工具层支持
    WORKER --> LOGGER
    WORKER --> TRACING
    WORKER --> CLEANUP
    WORKER --> RETRY

    %% 基础设施
    WORKER --> FILES
    WORKER --> CONFIG
    RESULT_MGR --> REDIS

    %% 外部回调
    CALLBACK --> EXT
    FILTER --> EXT

    %% 样式
    classDef external fill:#ffcccc,stroke:#ff6666,stroke-width:2px
    classDef api fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef business fill:#ccffcc,stroke:#00cc00,stroke-width:2px
    classDef core fill:#ffffcc,stroke:#cccc00,stroke-width:2px
    classDef utils fill:#ffccff,stroke:#cc00cc,stroke-width:2px
    classDef infra fill:#cccccc,stroke:#666666,stroke-width:2px

    class EXT,USER,BOSS external
    class FASTAPI,AGENT_API,CTRL_API,DISPATCH_API,RESULT_MGR api
    class LOGIN,FETCH,BUILD,FILTER,CALLBACK business
    class BROWSER,TASK_PROC,WORKER core
    class LOGGER,TRACING,CLEANUP,RETRY utils
    class REDIS,FILES,CONFIG infra
```


### 2. 候选人筛选业务流程图
**用途**: 详细展示候选人筛选的完整业务流程
**内容**:
- 任务接收和验证
- 登录流程（Cookie登录、二维码登录）
- 页面导航和数据抓取
- 信息提取和结构化
- 智能筛选和结果处理
- 错误处理和重试机制

**关键特点**:
- 完整的业务流程覆盖
- 多种登录方式支持
- 异常处理路径
- 循环处理逻辑
```mermaid
flowchart TD
    START([开始筛选任务]) --> RECV_REQ[接收筛选请求]
    RECV_REQ --> VALIDATE[验证请求参数]
    VALIDATE --> |参数有效| GEN_TASK[生成任务ID]
    VALIDATE --> |参数无效| ERROR_PARAM[返回参数错误]
    
    GEN_TASK --> QUEUE_TASK[任务入队Redis]
    QUEUE_TASK --> WAIT_WORKER[等待Worker处理]
    
    WAIT_WORKER --> WORKER_GET[Worker获取任务]
    WORKER_GET --> CHECK_LOGIN{检查登录状态}
    
    CHECK_LOGIN --> |已登录| NAV_PAGE[导航到推荐页面]
    CHECK_LOGIN --> |未登录| LOGIN_FLOW[执行登录流程]
    
    LOGIN_FLOW --> COOKIE_LOGIN{尝试Cookie登录}
    COOKIE_LOGIN --> |成功| SAVE_SESSION[保存会话状态]
    COOKIE_LOGIN --> |失败| QR_LOGIN[获取二维码]
    QR_LOGIN --> WAIT_SCAN[等待用户扫码]
    WAIT_SCAN --> |扫码成功| SAVE_SESSION
    WAIT_SCAN --> |超时| LOGIN_TIMEOUT[登录超时]
    
    SAVE_SESSION --> NAV_PAGE
    NAV_PAGE --> SELECT_JOB[选择指定职位]
    SELECT_JOB --> WAIT_LOAD[等待页面加载]
    
    WAIT_LOAD --> START_FETCH[开始抓取循环]
    START_FETCH --> SCROLL_PAGE[模拟滚动页面]
    SCROLL_PAGE --> INTERCEPT_API[拦截API请求]
    INTERCEPT_API --> GET_CANVAS[获取Canvas文本]
    
    GET_CANVAS --> EXTRACT_INFO[提取候选人信息]
    EXTRACT_INFO --> HYBRID_BUILD[混合数据构建]
    HYBRID_BUILD --> STRUCT_DATA[生成结构化数据]
    
    STRUCT_DATA --> SEND_FILTER[发送到SRAA筛选]
    SEND_FILTER --> WAIT_RESULT[等待筛选结果]
    WAIT_RESULT --> PROCESS_RESULT{处理筛选结果}
    
    PROCESS_RESULT --> |继续| NEXT_CANDIDATE[点击下一个候选人]
    PROCESS_RESULT --> |收藏| LIKE_ACTION[执行收藏操作]
    PROCESS_RESULT --> |停止| TASK_COMPLETE[任务完成]
    PROCESS_RESULT --> |跳过| SKIP_CANDIDATE[跳过当前候选人]
    
    LIKE_ACTION --> RECORD_ACTION[记录操作结果]
    RECORD_ACTION --> NEXT_CANDIDATE
    SKIP_CANDIDATE --> NEXT_CANDIDATE
    NEXT_CANDIDATE --> CHECK_CONTINUE{是否继续}
    
    CHECK_CONTINUE --> |是| SCROLL_PAGE
    CHECK_CONTINUE --> |否| TASK_COMPLETE
    
    TASK_COMPLETE --> SEND_CALLBACK[发送完成回调]
    SEND_CALLBACK --> UPDATE_STATUS[更新任务状态]
    UPDATE_STATUS --> CLEANUP[清理资源]
    CLEANUP --> END([任务结束])
    
    %% 错误处理
    ERROR_PARAM --> END
    LOGIN_TIMEOUT --> ERROR_LOGIN[登录失败]
    ERROR_LOGIN --> SEND_ERROR_CALLBACK[发送错误回调]
    SEND_ERROR_CALLBACK --> END
    
    %% 异常处理
    INTERCEPT_API --> |网络异常| RETRY_NETWORK[网络重试]
    RETRY_NETWORK --> |重试成功| GET_CANVAS
    RETRY_NETWORK --> |重试失败| NETWORK_ERROR[网络错误]
    NETWORK_ERROR --> SEND_ERROR_CALLBACK
    
    SEND_FILTER --> |服务异常| RETRY_FILTER[筛选重试]
    RETRY_FILTER --> |重试成功| WAIT_RESULT
    RETRY_FILTER --> |重试失败| FILTER_ERROR[筛选服务错误]
    FILTER_ERROR --> SEND_ERROR_CALLBACK
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class START,END startEnd
    class RECV_REQ,VALIDATE,GEN_TASK,QUEUE_TASK,WORKER_GET,NAV_PAGE,SELECT_JOB,WAIT_LOAD,START_FETCH,SCROLL_PAGE,INTERCEPT_API,GET_CANVAS,EXTRACT_INFO,HYBRID_BUILD,STRUCT_DATA,SEND_FILTER,WAIT_RESULT,NEXT_CANDIDATE,LIKE_ACTION,RECORD_ACTION,SKIP_CANDIDATE,TASK_COMPLETE,SEND_CALLBACK,UPDATE_STATUS,CLEANUP process
    class CHECK_LOGIN,COOKIE_LOGIN,PROCESS_RESULT,CHECK_CONTINUE decision
    class ERROR_PARAM,LOGIN_TIMEOUT,ERROR_LOGIN,NETWORK_ERROR,FILTER_ERROR,SEND_ERROR_CALLBACK error
    class SAVE_SESSION,QR_LOGIN,WAIT_SCAN success
```


### 3. 系统数据流向图
**用途**: 展示数据在系统中的流转过程和处理层次
**内容**:
- 外部数据源（用户请求、Boss直聘API/Canvas、SRAA服务）
- 数据接入层（API网关、验证器、任务生成器）
- 数据队列层（Redis任务队列、状态存储、结果缓存）
- 数据处理层（Worker、拦截器、提取器、融合器、构建器）
- 数据存储层（日志、Trace、会话、临时数据）
- 数据输出层（结果管理、回调处理、状态报告）

**关键特点**:
- 数据流向清晰标注
- 数据类型说明
- 处理层次分明
- 存储和缓存策略
```mermaid
graph LR
    subgraph "外部数据源"
        USER_REQ[用户请求]
        BOSS_API[Boss直聘API]
        BOSS_CANVAS[Boss直聘Canvas]
        SRAA_SERVICE[SRAA筛选服务]
    end

    subgraph "数据接入层"
        API_GATEWAY[API网关]
        REQUEST_VALIDATOR[请求验证器]
        TASK_GENERATOR[任务生成器]
    end

    subgraph "数据队列层"
        REDIS_QUEUE[(Redis任务队列)]
        REDIS_STATUS[(Redis状态存储)]
        REDIS_RESULT[(Redis结果缓存)]
    end

    subgraph "数据处理层"
        WORKER_PROC[Worker处理器]
        API_INTERCEPTOR[API拦截器]
        CANVAS_EXTRACTOR[Canvas提取器]
        DATA_MERGER[数据融合器]
        STRUCT_BUILDER[结构化构建器]
    end

    subgraph "数据存储层"
        LOG_FILES[日志文件]
        TRACE_FILES[Trace文件]
        SESSION_DATA[会话数据]
        TEMP_DATA[临时数据]
    end

    subgraph "数据输出层"
        RESULT_MANAGER[结果管理器]
        CALLBACK_HANDLER[回调处理器]
        STATUS_REPORTER[状态报告器]
    end

    %% 数据流向
    USER_REQ --> API_GATEWAY
    API_GATEWAY --> REQUEST_VALIDATOR
    REQUEST_VALIDATOR --> TASK_GENERATOR
    TASK_GENERATOR --> REDIS_QUEUE

    REDIS_QUEUE --> WORKER_PROC
    WORKER_PROC --> REDIS_STATUS
    WORKER_PROC --> API_INTERCEPTOR
    WORKER_PROC --> CANVAS_EXTRACTOR

    BOSS_API --> API_INTERCEPTOR
    BOSS_CANVAS --> CANVAS_EXTRACTOR

    API_INTERCEPTOR --> DATA_MERGER
    CANVAS_EXTRACTOR --> DATA_MERGER
    DATA_MERGER --> STRUCT_BUILDER

    STRUCT_BUILDER --> SRAA_SERVICE
    SRAA_SERVICE --> WORKER_PROC

    WORKER_PROC --> LOG_FILES
    WORKER_PROC --> TRACE_FILES
    WORKER_PROC --> SESSION_DATA
    WORKER_PROC --> TEMP_DATA

    WORKER_PROC --> REDIS_RESULT
    REDIS_RESULT --> RESULT_MANAGER
    RESULT_MANAGER --> CALLBACK_HANDLER
    CALLBACK_HANDLER --> SRAA_SERVICE

    REDIS_STATUS --> STATUS_REPORTER
    STATUS_REPORTER --> API_GATEWAY

    %% 数据类型标注
    USER_REQ -.->|JSON请求| API_GATEWAY
    BOSS_API -.->|候选人JSON| API_INTERCEPTOR
    BOSS_CANVAS -.->|Canvas文本| CANVAS_EXTRACTOR
    DATA_MERGER -.->|融合数据| STRUCT_BUILDER
    STRUCT_BUILDER -.->|结构化简历| SRAA_SERVICE
    SRAA_SERVICE -.->|筛选结果| WORKER_PROC
    WORKER_PROC -.->|任务结果| REDIS_RESULT
    CALLBACK_HANDLER -.->|状态回调| SRAA_SERVICE

    %% 样式定义
    classDef external fill:#ffcccc,stroke:#ff6666,stroke-width:2px
    classDef input fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef queue fill:#ffffcc,stroke:#cccc00,stroke-width:2px
    classDef process fill:#ccffcc,stroke:#00cc00,stroke-width:2px
    classDef storage fill:#ffccff,stroke:#cc00cc,stroke-width:2px
    classDef output fill:#ccccff,stroke:#6666cc,stroke-width:2px

    class USER_REQ,BOSS_API,BOSS_CANVAS,SRAA_SERVICE external
    class API_GATEWAY,REQUEST_VALIDATOR,TASK_GENERATOR input
    class REDIS_QUEUE,REDIS_STATUS,REDIS_RESULT queue
    class WORKER_PROC,API_INTERCEPTOR,CANVAS_EXTRACTOR,DATA_MERGER,STRUCT_BUILDER process
    class LOG_FILES,TRACE_FILES,SESSION_DATA,TEMP_DATA storage
    class RESULT_MANAGER,CALLBACK_HANDLER,STATUS_REPORTER output
```


### 4. 任务处理器架构图
**用途**: 展示任务处理器的设计模式和组件关系
**内容**:
- 任务分发层（Redis队列、分发器、验证器）
- 任务处理器工厂（工厂模式、注册表）
- 具体任务处理器（登录、简历详情、筛选、职位列表）
- 基础任务处理器（参数验证、任务执行、处理流程）
- 支持服务（浏览器管理、追踪、日志、重试）
- 业务流程（各种业务流程模块）

**关键特点**:
- 工厂设计模式
- 继承关系清晰
- 任务类型映射
- 支持服务集成
```mermaid
graph TD
    subgraph "任务分发层"
        TASK_QUEUE[Redis任务队列]
        TASK_DISPATCHER[任务分发器]
        TASK_VALIDATOR[任务验证器]
    end

    subgraph "任务处理器工厂"
        PROCESSOR_FACTORY[TaskProcessor工厂]
        PROCESSOR_REGISTRY[处理器注册表]
    end

    subgraph "具体任务处理器"
        LOGIN_PROCESSOR[LoginProcessor<br/>登录处理器]
        RESUME_PROCESSOR[ResumeDetailProcessor<br/>简历详情处理器]
        FILTER_PROCESSOR[JobFilterProcessor<br/>筛选处理器]
        JOBLIST_PROCESSOR[JobListProcessor<br/>职位列表处理器]
    end

    subgraph "基础任务处理器"
        BASE_PROCESSOR[BaseTaskProcessor<br/>基础处理器]
        VALIDATE_PARAMS[参数验证]
        EXECUTE_TASK[任务执行]
        PROCESS_FLOW[处理流程]
    end

    subgraph "支持服务"
        BROWSER_MGR[浏览器管理器]
        TRACING_MGR[追踪管理器]
        LOGGER_SVC[日志服务]
        RETRY_HANDLER[重试处理器]
    end

    subgraph "业务流程"
        LOGIN_FLOW[登录流程]
        FETCH_FLOW[抓取流程]
        BUILD_FLOW[构建流程]
        FILTER_FLOW[筛选流程]
        CALLBACK_FLOW[回调流程]
    end

    %% 任务流转
    TASK_QUEUE --> TASK_DISPATCHER
    TASK_DISPATCHER --> TASK_VALIDATOR
    TASK_VALIDATOR --> PROCESSOR_FACTORY

    %% 处理器创建
    PROCESSOR_FACTORY --> PROCESSOR_REGISTRY
    PROCESSOR_REGISTRY --> |login| LOGIN_PROCESSOR
    PROCESSOR_REGISTRY --> |get_resume_detail| RESUME_PROCESSOR
    PROCESSOR_REGISTRY --> |jobFilterTrigger| FILTER_PROCESSOR
    PROCESSOR_REGISTRY --> |get_job_list| JOBLIST_PROCESSOR

    %% 继承关系
    BASE_PROCESSOR --> LOGIN_PROCESSOR
    BASE_PROCESSOR --> RESUME_PROCESSOR
    BASE_PROCESSOR --> FILTER_PROCESSOR
    BASE_PROCESSOR --> JOBLIST_PROCESSOR

    %% 基础处理流程
    BASE_PROCESSOR --> VALIDATE_PARAMS
    BASE_PROCESSOR --> EXECUTE_TASK
    BASE_PROCESSOR --> PROCESS_FLOW

    %% 支持服务调用
    LOGIN_PROCESSOR --> BROWSER_MGR
    RESUME_PROCESSOR --> BROWSER_MGR
    FILTER_PROCESSOR --> BROWSER_MGR
    JOBLIST_PROCESSOR --> BROWSER_MGR

    BASE_PROCESSOR --> TRACING_MGR
    BASE_PROCESSOR --> LOGGER_SVC
    BASE_PROCESSOR --> RETRY_HANDLER

    %% 业务流程调用
    LOGIN_PROCESSOR --> LOGIN_FLOW
    RESUME_PROCESSOR --> FETCH_FLOW
    FILTER_PROCESSOR --> FETCH_FLOW
    FILTER_PROCESSOR --> BUILD_FLOW
    FILTER_PROCESSOR --> FILTER_FLOW
    FILTER_PROCESSOR --> CALLBACK_FLOW

    %% 任务类型映射
    TASK_QUEUE -.->|action: login| LOGIN_PROCESSOR
    TASK_QUEUE -.->|action: get_resume_detail| RESUME_PROCESSOR
    TASK_QUEUE -.->|action: jobFilterTrigger| FILTER_PROCESSOR
    TASK_QUEUE -.->|action: get_job_list| JOBLIST_PROCESSOR

    %% 样式定义
    classDef queue fill:#ffffcc,stroke:#cccc00,stroke-width:2px
    classDef factory fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef processor fill:#ccffcc,stroke:#00cc00,stroke-width:2px
    classDef base fill:#ffccff,stroke:#cc00cc,stroke-width:2px
    classDef service fill:#ffcccc,stroke:#ff6666,stroke-width:2px
    classDef flow fill:#ccccff,stroke:#6666cc,stroke-width:2px

    class TASK_QUEUE,TASK_DISPATCHER,TASK_VALIDATOR queue
    class PROCESSOR_FACTORY,PROCESSOR_REGISTRY factory
    class LOGIN_PROCESSOR,RESUME_PROCESSOR,FILTER_PROCESSOR,JOBLIST_PROCESSOR processor
    class BASE_PROCESSOR,VALIDATE_PARAMS,EXECUTE_TASK,PROCESS_FLOW base
    class BROWSER_MGR,TRACING_MGR,LOGGER_SVC,RETRY_HANDLER service
    class LOGIN_FLOW,FETCH_FLOW,BUILD_FLOW,FILTER_FLOW,CALLBACK_FLOW flow
```


### 5. 混合数据提取流程图
**用途**: 详细展示如何从API数据和Canvas文本中提取和融合信息
**内容**:
- API数据处理（解析、基础信息提取、教育经历、工作概要）
- Canvas文本处理（清理、重建、分段、详细提取）
- 文本分段解析（工作、项目、教育、证书、技能段落）
- 详细信息提取（工作详情、项目详情、技能、证书、时间解析）
- 数据融合处理（融合器、冲突解决、数据增强、质量检查）
- 结构化输出（构建器、各类信息、最终输出）

**关键特点**:
- 多源数据融合
- 智能文本处理
- 结构化数据构建
- 质量控制机制
```mermaid
flowchart TD
    START([开始数据提取]) --> GET_API[获取API数据]
    GET_API --> GET_CANVAS[获取Canvas文本]
    
    subgraph "API数据处理"
        API_DATA[API原始数据]
        PARSE_API[解析API数据]
        EXTRACT_BASIC[提取基础信息]
        EXTRACT_EDU[提取教育经历]
        EXTRACT_WORK_API[提取工作经历概要]
    end
    
    subgraph "Canvas文本处理"
        CANVAS_TEXT[Canvas原始文本]
        CLEAN_TEXT[清理文本数据]
        REBUILD_LINES[重建文本行]
        SECTION_SPLIT[分段处理]
        EXTRACT_DETAILS[提取详细信息]
    end
    
    subgraph "文本分段解析"
        WORK_SECTION[工作经历段落]
        PROJECT_SECTION[项目经历段落]
        EDU_SECTION[教育经历段落]
        CERT_SECTION[证书资质段落]
        SKILL_SECTION[技能标签段落]
    end
    
    subgraph "详细信息提取"
        WORK_DETAILS[工作详情提取]
        PROJECT_DETAILS[项目详情提取]
        SKILL_EXTRACT[技能提取]
        CERT_EXTRACT[证书提取]
        TIME_PARSE[时间解析]
    end
    
    subgraph "数据融合处理"
        DATA_MERGER[数据融合器]
        CONFLICT_RESOLVE[冲突解决]
        DATA_ENHANCE[数据增强]
        QUALITY_CHECK[质量检查]
    end
    
    subgraph "结构化输出"
        STRUCT_BUILDER[结构化构建器]
        CANDIDATE_INFO[候选人基础信息]
        WORK_EXP[工作经历列表]
        PROJECT_EXP[项目经历列表]
        EDUCATION[教育背景]
        EXPECTATIONS[期望职位]
        FINAL_OUTPUT[最终结构化数据]
    end
    
    %% API数据流
    GET_API --> API_DATA
    API_DATA --> PARSE_API
    PARSE_API --> EXTRACT_BASIC
    PARSE_API --> EXTRACT_EDU
    PARSE_API --> EXTRACT_WORK_API
    
    %% Canvas文本流
    GET_CANVAS --> CANVAS_TEXT
    CANVAS_TEXT --> CLEAN_TEXT
    CLEAN_TEXT --> REBUILD_LINES
    REBUILD_LINES --> SECTION_SPLIT
    
    %% 分段处理
    SECTION_SPLIT --> WORK_SECTION
    SECTION_SPLIT --> PROJECT_SECTION
    SECTION_SPLIT --> EDU_SECTION
    SECTION_SPLIT --> CERT_SECTION
    SECTION_SPLIT --> SKILL_SECTION
    
    %% 详细提取
    WORK_SECTION --> WORK_DETAILS
    PROJECT_SECTION --> PROJECT_DETAILS
    SKILL_SECTION --> SKILL_EXTRACT
    CERT_SECTION --> CERT_EXTRACT
    WORK_SECTION --> TIME_PARSE
    PROJECT_SECTION --> TIME_PARSE
    
    %% 数据融合
    EXTRACT_BASIC --> DATA_MERGER
    EXTRACT_EDU --> DATA_MERGER
    EXTRACT_WORK_API --> DATA_MERGER
    WORK_DETAILS --> DATA_MERGER
    PROJECT_DETAILS --> DATA_MERGER
    SKILL_EXTRACT --> DATA_MERGER
    CERT_EXTRACT --> DATA_MERGER
    
    DATA_MERGER --> CONFLICT_RESOLVE
    CONFLICT_RESOLVE --> DATA_ENHANCE
    DATA_ENHANCE --> QUALITY_CHECK
    
    %% 结构化构建
    QUALITY_CHECK --> STRUCT_BUILDER
    STRUCT_BUILDER --> CANDIDATE_INFO
    STRUCT_BUILDER --> WORK_EXP
    STRUCT_BUILDER --> PROJECT_EXP
    STRUCT_BUILDER --> EDUCATION
    STRUCT_BUILDER --> EXPECTATIONS
    
    CANDIDATE_INFO --> FINAL_OUTPUT
    WORK_EXP --> FINAL_OUTPUT
    PROJECT_EXP --> FINAL_OUTPUT
    EDUCATION --> FINAL_OUTPUT
    EXPECTATIONS --> FINAL_OUTPUT
    
    FINAL_OUTPUT --> END([提取完成])
    
    %% 数据类型标注
    API_DATA -.->|JSON格式| PARSE_API
    CANVAS_TEXT -.->|文本字符串| CLEAN_TEXT
    WORK_DETAILS -.->|工作描述+技能| DATA_MERGER
    PROJECT_DETAILS -.->|项目描述+技能| DATA_MERGER
    DATA_MERGER -.->|融合数据| STRUCT_BUILDER
    FINAL_OUTPUT -.->|结构化JSON| END
    
    %% 样式定义
    classDef start fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef canvas fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef section fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef extract fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef merge fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef output fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    
    class START,END start
    class API_DATA,PARSE_API,EXTRACT_BASIC,EXTRACT_EDU,EXTRACT_WORK_API api
    class CANVAS_TEXT,CLEAN_TEXT,REBUILD_LINES,SECTION_SPLIT,EXTRACT_DETAILS canvas
    class WORK_SECTION,PROJECT_SECTION,EDU_SECTION,CERT_SECTION,SKILL_SECTION section
    class WORK_DETAILS,PROJECT_DETAILS,SKILL_EXTRACT,CERT_EXTRACT,TIME_PARSE extract
    class DATA_MERGER,CONFLICT_RESOLVE,DATA_ENHANCE,QUALITY_CHECK merge
    class STRUCT_BUILDER,CANDIDATE_INFO,WORK_EXP,PROJECT_EXP,EDUCATION,EXPECTATIONS,FINAL_OUTPUT output
```


### 6. 系统监控和管理流程图
**用途**: 展示系统的监控、管理和自动化运维机制
**内容**:
- 监控数据源（Worker状态、任务指标、系统指标、错误日志、性能数据）
- 数据收集层（心跳监控、状态收集、指标收集、日志收集）
- 数据存储层（Redis状态、日志文件、Trace文件、指标缓存）
- 监控服务层（健康监控、性能监控、磁盘监控、错误监控、任务监控）
- 管理服务层（任务管理、清理管理、Worker控制、配置管理）
- 自动化服务（自动清理、日志归档、Trace清理、临时文件清理）
- 管理接口（控制API、管理命令、状态查询、清理操作）
- 告警通知（告警引擎、企业微信、邮件通知、监控面板）

**关键特点**:
- 全面的监控覆盖
- 自动化运维
- 多种告警方式
- 管理接口完善
```mermaid
graph TB
    subgraph "监控数据源"
        WORKER_STATUS[Worker状态]
        TASK_METRICS[任务指标]
        SYSTEM_METRICS[系统指标]
        ERROR_LOGS[错误日志]
        PERFORMANCE[性能数据]
    end

    subgraph "数据收集层"
        HEARTBEAT[心跳监控]
        STATUS_COLLECTOR[状态收集器]
        METRICS_COLLECTOR[指标收集器]
        LOG_COLLECTOR[日志收集器]
    end

    subgraph "数据存储层"
        REDIS_STATUS[(Redis状态存储)]
        LOG_FILES[(日志文件)]
        TRACE_FILES[(Trace文件)]
        METRICS_CACHE[(指标缓存)]
    end

    subgraph "监控服务层"
        HEALTH_MONITOR[健康监控]
        PERFORMANCE_MONITOR[性能监控]
        DISK_MONITOR[磁盘监控]
        ERROR_MONITOR[错误监控]
        TASK_MONITOR[任务监控]
    end

    subgraph "管理服务层"
        TASK_MANAGER[任务管理器]
        CLEANUP_MANAGER[清理管理器]
        WORKER_CONTROLLER[Worker控制器]
        CONFIG_MANAGER[配置管理器]
    end

    subgraph "自动化服务"
        AUTO_CLEANUP[自动清理服务]
        LOG_ARCHIVER[日志归档器]
        TRACE_CLEANER[Trace清理器]
        TEMP_CLEANER[临时文件清理器]
    end

    subgraph "管理接口"
        CTRL_API[控制API]
        ADMIN_COMMANDS[管理命令]
        STATUS_API[状态查询API]
        CLEANUP_API[清理操作API]
    end

    subgraph "告警和通知"
        ALERT_ENGINE[告警引擎]
        WECHAT_BOT[企业微信机器人]
        EMAIL_NOTIFIER[邮件通知]
        DASHBOARD[监控面板]
    end

    %% 数据收集流程
    WORKER_STATUS --> HEARTBEAT
    WORKER_STATUS --> STATUS_COLLECTOR
    TASK_METRICS --> METRICS_COLLECTOR
    SYSTEM_METRICS --> METRICS_COLLECTOR
    ERROR_LOGS --> LOG_COLLECTOR
    PERFORMANCE --> METRICS_COLLECTOR

    %% 数据存储流程
    HEARTBEAT --> REDIS_STATUS
    STATUS_COLLECTOR --> REDIS_STATUS
    METRICS_COLLECTOR --> METRICS_CACHE
    LOG_COLLECTOR --> LOG_FILES
    PERFORMANCE --> TRACE_FILES

    %% 监控服务流程
    REDIS_STATUS --> HEALTH_MONITOR
    METRICS_CACHE --> PERFORMANCE_MONITOR
    LOG_FILES --> DISK_MONITOR
    LOG_FILES --> ERROR_MONITOR
    REDIS_STATUS --> TASK_MONITOR

    %% 管理服务流程
    HEALTH_MONITOR --> TASK_MANAGER
    DISK_MONITOR --> CLEANUP_MANAGER
    TASK_MONITOR --> WORKER_CONTROLLER
    PERFORMANCE_MONITOR --> CONFIG_MANAGER

    %% 自动化服务流程
    CLEANUP_MANAGER --> AUTO_CLEANUP
    LOG_FILES --> LOG_ARCHIVER
    TRACE_FILES --> TRACE_CLEANER
    DISK_MONITOR --> TEMP_CLEANER

    %% 管理接口流程
    WORKER_CONTROLLER --> CTRL_API
    TASK_MANAGER --> ADMIN_COMMANDS
    HEALTH_MONITOR --> STATUS_API
    CLEANUP_MANAGER --> CLEANUP_API

    %% 告警通知流程
    ERROR_MONITOR --> ALERT_ENGINE
    DISK_MONITOR --> ALERT_ENGINE
    HEALTH_MONITOR --> ALERT_ENGINE
    ALERT_ENGINE --> WECHAT_BOT
    ALERT_ENGINE --> EMAIL_NOTIFIER
    HEALTH_MONITOR --> DASHBOARD

    %% 管理操作流程
    CTRL_API --> |暂停/恢复/重启| WORKER_CONTROLLER
    ADMIN_COMMANDS --> |清理命令| CLEANUP_MANAGER
    CLEANUP_API --> |手动清理| AUTO_CLEANUP

    %% 反馈循环
    WECHAT_BOT -.->|告警反馈| ALERT_ENGINE
    DASHBOARD -.->|状态查询| HEALTH_MONITOR
    AUTO_CLEANUP -.->|清理结果| DISK_MONITOR

    %% 数据流标注
    HEARTBEAT -.->|心跳数据| REDIS_STATUS
    METRICS_COLLECTOR -.->|性能指标| METRICS_CACHE
    ERROR_MONITOR -.->|错误统计| ALERT_ENGINE
    DISK_MONITOR -.->|磁盘使用率| ALERT_ENGINE

    %% 样式定义
    classDef source fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef collect fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef storage fill:#ffffcc,stroke:#cccc00,stroke-width:2px
    classDef monitor fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef manage fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef auto fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef api fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef alert fill:#ffebee,stroke:#c62828,stroke-width:2px

    class WORKER_STATUS,TASK_METRICS,SYSTEM_METRICS,ERROR_LOGS,PERFORMANCE source
    class HEARTBEAT,STATUS_COLLECTOR,METRICS_COLLECTOR,LOG_COLLECTOR collect
    class REDIS_STATUS,LOG_FILES,TRACE_FILES,METRICS_CACHE storage
    class HEALTH_MONITOR,PERFORMANCE_MONITOR,DISK_MONITOR,ERROR_MONITOR,TASK_MONITOR monitor
    class TASK_MANAGER,CLEANUP_MANAGER,WORKER_CONTROLLER,CONFIG_MANAGER manage
    class AUTO_CLEANUP,LOG_ARCHIVER,TRACE_CLEANER,TEMP_CLEANER auto
    class CTRL_API,ADMIN_COMMANDS,STATUS_API,CLEANUP_API api
    class ALERT_ENGINE,WECHAT_BOT,EMAIL_NOTIFIER,DASHBOARD alert
```


## 流程图使用指南

### 阅读顺序建议
1. **系统整体架构图** - 了解系统整体结构
2. **候选人筛选业务流程图** - 理解核心业务流程
3. **数据流向图** - 掌握数据处理过程
4. **任务处理器架构图** - 深入了解任务处理机制
5. **混合数据提取流程图** - 理解核心技术实现
6. **系统监控和管理流程图** - 了解运维和管理机制

### 颜色编码说明
- **红色系**: 外部系统和错误处理
- **蓝色系**: API层和接口
- **绿色系**: 业务流程和处理逻辑
- **黄色系**: 队列和存储
- **紫色系**: 工具和支持服务
- **灰色系**: 基础设施

### 符号说明
- **矩形**: 处理节点或组件
- **圆角矩形**: 开始/结束节点
- **菱形**: 决策节点
- **圆柱形**: 数据存储
- **实线箭头**: 数据流或控制流
- **虚线箭头**: 数据类型或补充说明

## 技术实现要点

### 架构设计原则
1. **分层架构**: 清晰的层次划分，职责分离
2. **模块化设计**: 高内聚、低耦合的模块设计
3. **可扩展性**: 支持新功能和新平台的扩展
4. **容错性**: 完善的错误处理和恢复机制
5. **可监控性**: 全面的监控和日志记录

### 关键技术特点
1. **异步任务处理**: 基于Redis的任务队列
2. **智能数据融合**: API数据与Canvas文本的混合提取
3. **反检测机制**: 模拟真实用户行为
4. **自动化运维**: 智能清理和资源管理
5. **实时监控**: 完善的状态监控和告警机制

### 性能优化策略
1. **资源管理**: 自动清理临时文件和缓存
2. **并发控制**: 合理的任务并发处理
3. **缓存策略**: 多层次的数据缓存
4. **错误恢复**: 智能重试和错误恢复
5. **性能监控**: 实时性能指标收集

## 总结

这些Mermaid流程图全面展示了SRA系统的设计思路和实现细节，从宏观架构到微观实现，从业务流程到技术细节，为理解和维护系统提供了重要的可视化参考。

通过这些流程图，开发者可以：
- 快速理解系统整体架构
- 掌握核心业务流程
- 了解数据处理机制
- 学习设计模式应用
- 理解监控运维体系

建议在系统开发、维护和扩展过程中，经常参考这些流程图，确保对系统的理解准确和完整。

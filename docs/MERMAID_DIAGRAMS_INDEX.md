# SRA系统 Mermaid流程图索引

本文档提供了SRA智能招聘助手系统的所有Mermaid流程图的索引和说明。这些流程图从不同角度展示了系统的架构、业务流程、数据流向和组件交互关系。

## 1. 系统整体架构与核心组件图

### 1.1 系统整体架构图 (Flowchart)
**用途**: 从最高层次展示SRA系统的分层架构和核心组件关系。

```mermaid
graph TD
    subgraph "外部系统"
        EXT[外部SRAA服务]
        USER[用户/管理员]
        BOSS[Boss直聘网站]
    end

    subgraph "SRA系统"
        subgraph "API层"
            FASTAPI[FastAPI Web服务]
            AGENT_API[Agent API<br/>业务接口]
            CTRL_API[Control API<br/>控制接口]
            DISPATCH_API[Dispatch API<br/>任务分发]
            RESULT_MGR[Result Manager<br/>结果管理器]
        end

        subgraph "业务流程层"
            LOGIN[Login Flow<br/>登录流程]
            FETCH[Geek Fetch Flow<br/>候选人抓取]
            BUILD[Geek Info Build<br/>信息构建]
            FILTER[Geek Filter<br/>智能筛选]
            CALLBACK[Callback<br/>回调处理]
        end

        subgraph "核心层"
            BROWSER[Browser Manager<br/>浏览器管理]
            TASK_PROC[Task Processor<br/>任务处理器]
            WORKER[Celery Worker<br/>工作进程]
        end

        subgraph "工具层"
            LOGGER[Logger<br/>日志管理]
            TRACING[Tracing Manager<br/>追踪管理]
            CLEANUP[Auto Cleanup<br/>自动清理]
        end

        subgraph "基础设施"
            REDIS[(Redis<br/>任务队列/状态存储)]
            FILES[文件系统<br/>日志/数据/trace]
            CONFIG[配置管理]
        end
    end

    %% 外部交互
    USER --> FASTAPI
    FASTAPI --> EXT
    WORKER --> BOSS

    %% API层内部
    FASTAPI --> AGENT_API
    FASTAPI --> CTRL_API
    FASTAPI --> DISPATCH_API
    AGENT_API --> RESULT_MGR
    DISPATCH_API --> RESULT_MGR

    %% API到队列
    AGENT_API --> REDIS
    CTRL_API --> REDIS
    DISPATCH_API --> REDIS

    %% Worker到业务流程
    WORKER --> LOGIN
    WORKER --> FETCH
    WORKER --> BUILD
    WORKER --> FILTER
    WORKER --> CALLBACK

    %% 业务流程内部
    LOGIN --> FETCH
    FETCH --> BUILD
    BUILD --> FILTER
    FILTER --> CALLBACK

    %% 核心层
    WORKER --> TASK_PROC
    TASK_PROC --> BROWSER
    REDIS --> WORKER

    %% 基础设施
    WORKER --> FILES
    WORKER --> CONFIG
    RESULT_MGR --> REDIS

    %% 外部回调
    CALLBACK --> EXT
    FILTER --> EXT

    %% 样式
    classDef external fill:#ffcccc,stroke:#ff6666,stroke-width:2px
    classDef api fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef business fill:#ccffcc,stroke:#00cc00,stroke-width:2px
    classDef core fill:#ffffcc,stroke:#cccc00,stroke-width:2px
    classDef utils fill:#ffccff,stroke:#cc00cc,stroke-width:2px
    classDef infra fill:#cccccc,stroke:#666666,stroke-width:2px

    class EXT,USER,BOSS external
    class FASTAPI,AGENT_API,CTRL_API,DISPATCH_API,RESULT_MGR api
    class LOGIN,FETCH,BUILD,FILTER,CALLBACK business
    class BROWSER,TASK_PROC,WORKER core
    class LOGGER,TRACING,CLEANUP utils
    class REDIS,FILES,CONFIG infra
```

### 1.2 核心组件类图 (Class Diagram)
**用途**: 展示系统中的主要类及其关系，突出了模块化和依赖注入的设计思想。

```mermaid
classDiagram
    direction LR

    class FastAPI_App {
        +include_router(router)
        +on_event("startup")
        +on_event("shutdown")
    }

    class APIRouter {
        +get(path, ...)
        +post(path, ...)
    }
    FastAPI_App --o APIRouter : "包含"

    class ResultManager {
        - _pending_tasks: dict
        - _listener_task: Task
        +initialize()
        +wait_for_result(task_id, timeout)
        +shutdown()
    }
    FastAPI_App ..> ResultManager : "管理"

    class CeleryWorker {
        +browser_manager()
        +main_loop()
        +cleanup_resources()
    }

    class TaskHandlerManager {
        +handle_task(page, task, redis)
        +handle_exception_and_enter_management_mode(...)
    }
    CeleryWorker ..> TaskHandlerManager : "使用"

    class BaseTaskProcessor {
        <<Abstract>>
        +page: Page
        +process(task)
        +validate_params(payload)
        +execute_task(payload)
    }

    class JobFilterProcessor {
        +execute_task(payload)
    }
    BaseTaskProcessor <|-- JobFilterProcessor

    class LoginProcessor {
        +execute_task(payload)
    }
    BaseTaskProcessor <|-- LoginProcessor

    TaskHandlerManager ..> BaseTaskProcessor : "获取并使用"

    class BrowserManager {
        +init_driver(auto_load_cookies)
        +simulate_human_scroll_div(...)
        +simulate_canvas_scroll(...)
    }
    CeleryWorker ..> BrowserManager : "调用"
    BaseTaskProcessor ..> BrowserManager : "持有 Page 实例"

    class HeartbeatMonitorManager {
        +start_monitoring(redis)
        +stop_monitoring()
        +update_status(...)
    }
    CeleryWorker ..> HeartbeatMonitorManager : "使用"

    class BrowserMonitorManager {
        +start_monitoring(page, redis)
        +stop_monitoring()
        +detect_browser_abnormal_closure(...)
    }
    CeleryWorker ..> BrowserMonitorManager : "使用"

    class DiskCleanupManager {
        +cleanup_playwright_temp()
        +emergency_cleanup()
    }
    CeleryWorker ..> DiskCleanupManager : "使用"

```

---

## 2. 核心任务交互图 (Sequence Diagram)

**用途**: 详细描绘一个 `jobFilterTrigger` 任务从被 API 接收到最终完成的完整调用链，清晰地展示了对象之间的交互和消息传递顺序。

```mermaid
sequenceDiagram
    actor User
    participant AgentAPI as FastAPI
    participant Redis
    participant CeleryWorker
    participant TaskProcessor
    participant GeekFetchFlow

    User->>AgentAPI: POST /agent/jobfilter/trigger
    activate AgentAPI
    AgentAPI->>Redis: LPUSH (将任务推入队列)
    AgentAPI-->>User: HTTP 200 OK (任务已派发)
    deactivate AgentAPI

    loop 主循环
        CeleryWorker->>Redis: GET (获取任务)
    end
    activate CeleryWorker

    Note over CeleryWorker: 检查预检标志和锁
    alt 预检未完成且未被锁定
        CeleryWorker->>Redis: SET (获取预检锁)
        Note over CeleryWorker: 获取锁成功，继续执行
        
        CeleryWorker->>TaskProcessor: process(task)
        activate TaskProcessor

        TaskProcessor->>GeekFetchFlow: fetch_recommended_geeks(...)
        activate GeekFetchFlow

        GeekFetchFlow->>GeekFetchFlow: (执行登录, 页面导航, 滚动, 数据提取等)

        Note over GeekFetchFlow: 任务成功执行到关键点
        GeekFetchFlow->>Redis: SET (设置预检完成标志)
        GeekFetchFlow->>Redis: DEL (释放预检锁)

        GeekFetchFlow-->>TaskProcessor: 返回结果
        deactivate GeekFetchFlow

        TaskProcessor-->>CeleryWorker: 返回结果
        deactivate TaskProcessor

        CeleryWorker->>Redis: SETEX (保存最终任务结果)
        
    else 预检正在进行中
        CeleryWorker->>CeleryWorker: asyncio.sleep(10)
        Note over CeleryWorker: 等待后重新检查
    end
    
    deactivate CeleryWorker
```

---

## 3. 业务流程图 (Business Flowcharts)

### 候选人信息提取流程 (Geek Info Extraction Flow)
**用途**: 详细展示如何从一个复杂的简历页面（包含 Canvas、DOM 和 API 数据）中，一步步提取并融合成一份结构化的 JSON 数据。
候选人筛选业务流程图
**用途**: 详细展示候选人筛选的完整业务流程
**内容**:
- 任务接收和验证
- 登录流程（Cookie登录、二维码登录）
- 页面导航和数据抓取
- 信息提取和结构化
- 智能筛选和结果处理
- 错误处理和重试机制

**关键特点**:
- 完整的业务流程覆盖
- 多种登录方式支持
- 异常处理路径
- 循环处理逻辑
```mermaid
flowchart TD
    START([开始筛选任务]) --> RECV_REQ[接收筛选请求]
    RECV_REQ --> VALIDATE[验证请求参数]
    VALIDATE --> |参数有效| GEN_TASK[生成任务ID]
    VALIDATE --> |参数无效| ERROR_PARAM[返回参数错误]
    
    GEN_TASK --> QUEUE_TASK[任务入队Redis]
    QUEUE_TASK --> WAIT_WORKER[等待Worker处理]
    
    WAIT_WORKER --> WORKER_GET[Worker获取任务]
    WORKER_GET --> CHECK_LOGIN{检查登录状态}
    
    CHECK_LOGIN --> |已登录| NAV_PAGE[导航到推荐页面]
    CHECK_LOGIN --> |未登录| LOGIN_FLOW[执行登录流程]
    
    LOGIN_FLOW --> COOKIE_LOGIN{尝试Cookie登录}
    COOKIE_LOGIN --> |成功| SAVE_SESSION[保存会话状态]
    COOKIE_LOGIN --> |失败| QR_LOGIN[获取二维码]
    QR_LOGIN --> WAIT_SCAN[等待用户扫码]
    WAIT_SCAN --> |扫码成功| SAVE_SESSION
    WAIT_SCAN --> |超时| LOGIN_TIMEOUT[登录超时]
    
    SAVE_SESSION --> NAV_PAGE
    NAV_PAGE --> SELECT_JOB[选择指定职位]
    SELECT_JOB --> WAIT_LOAD[等待页面加载]
    
    WAIT_LOAD --> START_FETCH[开始抓取循环]
    START_FETCH --> SCROLL_PAGE[模拟滚动页面]
    SCROLL_PAGE --> INTERCEPT_API[拦截API请求]
    INTERCEPT_API --> GET_CANVAS[获取Canvas文本]
    
    GET_CANVAS --> EXTRACT_INFO[提取候选人信息]
    EXTRACT_INFO --> HYBRID_BUILD[混合数据构建]
    HYBRID_BUILD --> STRUCT_DATA[生成结构化数据]
    
    STRUCT_DATA --> SEND_FILTER[发送到SRAA筛选]
    SEND_FILTER --> WAIT_RESULT[等待筛选结果]
    WAIT_RESULT --> PROCESS_RESULT{处理筛选结果}
    
    PROCESS_RESULT --> |继续| NEXT_CANDIDATE[点击下一个候选人]
    PROCESS_RESULT --> |收藏| LIKE_ACTION[执行收藏操作]
    PROCESS_RESULT --> |停止| TASK_COMPLETE[任务完成]
    PROCESS_RESULT --> |跳过| SKIP_CANDIDATE[跳过当前候选人]
    
    LIKE_ACTION --> RECORD_ACTION[记录操作结果]
    RECORD_ACTION --> NEXT_CANDIDATE
    SKIP_CANDIDATE --> NEXT_CANDIDATE
    NEXT_CANDIDATE --> CHECK_CONTINUE{是否继续}
    
    CHECK_CONTINUE --> |是| SCROLL_PAGE
    CHECK_CONTINUE --> |否| TASK_COMPLETE
    
    TASK_COMPLETE --> SEND_CALLBACK[发送完成回调]
    SEND_CALLBACK --> UPDATE_STATUS[更新任务状态]
    UPDATE_STATUS --> CLEANUP[清理资源]
    CLEANUP --> END([任务结束])
    
    %% 错误处理
    ERROR_PARAM --> END
    LOGIN_TIMEOUT --> ERROR_LOGIN[登录失败]
    ERROR_LOGIN --> SEND_ERROR_CALLBACK[发送错误回调]
    SEND_ERROR_CALLBACK --> END
    
    %% 异常处理
    INTERCEPT_API --> |网络异常| RETRY_NETWORK[网络重试]
    RETRY_NETWORK --> |重试成功| GET_CANVAS
    RETRY_NETWORK --> |重试失败| NETWORK_ERROR[网络错误]
    NETWORK_ERROR --> SEND_ERROR_CALLBACK
    
    SEND_FILTER --> |服务异常| RETRY_FILTER[筛选重试]
    RETRY_FILTER --> |重试成功| WAIT_RESULT
    RETRY_FILTER --> |重试失败| FILTER_ERROR[筛选服务错误]
    FILTER_ERROR --> SEND_ERROR_CALLBACK
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class START,END startEnd
    class RECV_REQ,VALIDATE,GEN_TASK,QUEUE_TASK,WORKER_GET,NAV_PAGE,SELECT_JOB,WAIT_LOAD,START_FETCH,SCROLL_PAGE,INTERCEPT_API,GET_CANVAS,EXTRACT_INFO,HYBRID_BUILD,STRUCT_DATA,SEND_FILTER,WAIT_RESULT,NEXT_CANDIDATE,LIKE_ACTION,RECORD_ACTION,SKIP_CANDIDATE,TASK_COMPLETE,SEND_CALLBACK,UPDATE_STATUS,CLEANUP process
    class CHECK_LOGIN,COOKIE_LOGIN,PROCESS_RESULT,CHECK_CONTINUE decision
    class ERROR_PARAM,LOGIN_TIMEOUT,ERROR_LOGIN,NETWORK_ERROR,FILTER_ERROR,SEND_ERROR_CALLBACK error
    class SAVE_SESSION,QR_LOGIN,WAIT_SCAN success
```

### 简历混合数据提取流程图
**用途**: 详细展示如何从API数据和Canvas文本中提取和融合信息
**内容**:
- API数据处理（解析、基础信息提取、教育经历、工作概要）
- Canvas文本处理（清理、重建、分段、详细提取）
- 文本分段解析（工作、项目、教育、证书、技能段落）
- 详细信息提取（工作详情、项目详情、技能、证书、时间解析）
- 数据融合处理（融合器、冲突解决、数据增强、质量检查）
- 结构化输出（构建器、各类信息、最终输出）

**关键特点**:
- 多源数据融合
- 智能文本处理
- 结构化数据构建
- 质量控制机制
```mermaid
flowchart TD
    START([开始数据提取]) --> GET_API[获取API数据]
    GET_API --> GET_CANVAS[获取Canvas文本]
    
    subgraph "API数据处理"
        API_DATA[API原始数据]
        PARSE_API[解析API数据]
        EXTRACT_BASIC[提取基础信息]
        EXTRACT_EDU[提取教育经历]
        EXTRACT_WORK_API[提取工作经历概要]
    end
    
    subgraph "Canvas文本处理"
        CANVAS_TEXT[Canvas原始文本]
        CLEAN_TEXT[清理文本数据]
        REBUILD_LINES[重建文本行]
        SECTION_SPLIT[分段处理]
        EXTRACT_DETAILS[提取详细信息]
    end
    
    subgraph "文本分段解析"
        WORK_SECTION[工作经历段落]
        PROJECT_SECTION[项目经历段落]
        EDU_SECTION[教育经历段落]
        CERT_SECTION[证书资质段落]
        SKILL_SECTION[技能标签段落]
    end
    
    subgraph "详细信息提取"
        WORK_DETAILS[工作详情提取]
        PROJECT_DETAILS[项目详情提取]
        SKILL_EXTRACT[技能提取]
        CERT_EXTRACT[证书提取]
        TIME_PARSE[时间解析]
    end
    
    subgraph "数据融合处理"
        DATA_MERGER[数据融合器]
        CONFLICT_RESOLVE[冲突解决]
        DATA_ENHANCE[数据增强]
        QUALITY_CHECK[质量检查]
    end
    
    subgraph "结构化输出"
        STRUCT_BUILDER[结构化构建器]
        CANDIDATE_INFO[候选人基础信息]
        WORK_EXP[工作经历列表]
        PROJECT_EXP[项目经历列表]
        EDUCATION[教育背景]
        EXPECTATIONS[期望职位]
        FINAL_OUTPUT[最终结构化数据]
    end
    
    %% API数据流
    GET_API --> API_DATA
    API_DATA --> PARSE_API
    PARSE_API --> EXTRACT_BASIC
    PARSE_API --> EXTRACT_EDU
    PARSE_API --> EXTRACT_WORK_API
    
    %% Canvas文本流
    GET_CANVAS --> CANVAS_TEXT
    CANVAS_TEXT --> CLEAN_TEXT
    CLEAN_TEXT --> REBUILD_LINES
    REBUILD_LINES --> SECTION_SPLIT
    
    %% 分段处理
    SECTION_SPLIT --> WORK_SECTION
    SECTION_SPLIT --> PROJECT_SECTION
    SECTION_SPLIT --> EDU_SECTION
    SECTION_SPLIT --> CERT_SECTION
    SECTION_SPLIT --> SKILL_SECTION
    
    %% 详细提取
    WORK_SECTION --> WORK_DETAILS
    PROJECT_SECTION --> PROJECT_DETAILS
    SKILL_SECTION --> SKILL_EXTRACT
    CERT_SECTION --> CERT_EXTRACT
    WORK_SECTION --> TIME_PARSE
    PROJECT_SECTION --> TIME_PARSE
    
    %% 数据融合
    EXTRACT_BASIC --> DATA_MERGER
    EXTRACT_EDU --> DATA_MERGER
    EXTRACT_WORK_API --> DATA_MERGER
    WORK_DETAILS --> DATA_MERGER
    PROJECT_DETAILS --> DATA_MERGER
    SKILL_EXTRACT --> DATA_MERGER
    CERT_EXTRACT --> DATA_MERGER
    
    DATA_MERGER --> CONFLICT_RESOLVE
    CONFLICT_RESOLVE --> DATA_ENHANCE
    DATA_ENHANCE --> QUALITY_CHECK
    
    %% 结构化构建
    QUALITY_CHECK --> STRUCT_BUILDER
    STRUCT_BUILDER --> CANDIDATE_INFO
    STRUCT_BUILDER --> WORK_EXP
    STRUCT_BUILDER --> PROJECT_EXP
    STRUCT_BUILDER --> EDUCATION
    STRUCT_BUILDER --> EXPECTATIONS
    
    CANDIDATE_INFO --> FINAL_OUTPUT
    WORK_EXP --> FINAL_OUTPUT
    PROJECT_EXP --> FINAL_OUTPUT
    EDUCATION --> FINAL_OUTPUT
    EXPECTATIONS --> FINAL_OUTPUT
    
    FINAL_OUTPUT --> END([提取完成])
    
    %% 数据类型标注
    API_DATA -.->|JSON格式| PARSE_API
    CANVAS_TEXT -.->|文本字符串| CLEAN_TEXT
    WORK_DETAILS -.->|工作描述+技能| DATA_MERGER
    PROJECT_DETAILS -.->|项目描述+技能| DATA_MERGER
    DATA_MERGER -.->|融合数据| STRUCT_BUILDER
    FINAL_OUTPUT -.->|结构化JSON| END
    
    %% 样式定义
    classDef start fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef canvas fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef section fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef extract fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef merge fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef output fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    
    class START,END start
    class API_DATA,PARSE_API,EXTRACT_BASIC,EXTRACT_EDU,EXTRACT_WORK_API api
    class CANVAS_TEXT,CLEAN_TEXT,REBUILD_LINES,SECTION_SPLIT,EXTRACT_DETAILS canvas
    class WORK_SECTION,PROJECT_SECTION,EDU_SECTION,CERT_SECTION,SKILL_SECTION section
    class WORK_DETAILS,PROJECT_DETAILS,SKILL_EXTRACT,CERT_EXTRACT,TIME_PARSE extract
    class DATA_MERGER,CONFLICT_RESOLVE,DATA_ENHANCE,QUALITY_CHECK merge
    class STRUCT_BUILDER,CANDIDATE_INFO,WORK_EXP,PROJECT_EXP,EDUCATION,EXPECTATIONS,FINAL_OUTPUT output
```

## 8. 错误处理和恢复流程图

### 8.1 异常处理流程图

```mermaid
graph TD
    A[任务执行] --> B{是否发生异常}
    B -->|否| C[正常完成]
    B -->|是| D{异常类型判断}

    D -->|TaskException| E[任务异常处理]
    D -->|BrowserException| F[浏览器异常处理]
    D -->|LoginException| G[登录异常处理]
    D -->|NetworkException| H[网络异常处理]
    D -->|其他异常| I[通用异常处理]

    E --> J[记录错误日志]
    F --> K[重启浏览器]
    G --> L[清理登录状态]
    H --> M[网络重试]
    I --> N[进入管理模式]

    J --> O{是否可重试}
    K --> P[浏览器重新初始化]
    L --> Q[重新登录流程]
    M --> R{重试次数检查}

    O -->|是| S[任务重试]
    O -->|否| T[任务失败]
    P --> U[恢复任务执行]
    Q --> V[继续原任务]
    R -->|未超限| W[延迟重试]
    R -->|已超限| X[标记失败]

    S --> A
    W --> A

    style D fill:#fff3e0
    style N fill:#ffebee
    style T fill:#ffcdd2
    style X fill:#ffcdd2
```



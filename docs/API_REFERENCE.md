# SRA API 参考文档

## 概述

SRA (Smart Recruitment Assistant) 提供了一套完整的RESTful API接口，用于管理和控制智能招聘助手的各项功能。所有API接口都基于HTTP协议，使用JSON格式进行数据交换。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

所有API接口都返回统一的JSON响应格式：

```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "success",
  "result": {
    "status": "success",
    "data": {}
  }
}
```

### 响应字段说明

- `timestamp`: 响应时间戳（毫秒）
- `code`: 响应状态码（0表示成功，非0表示错误）
- `message`: 响应消息
- `result`: 具体的响应数据

### 状态码说明

- `0`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

## API 接口详情

### 1. 系统健康检查

#### GET /health

检查系统健康状态。

**请求参数**: 无

**响应示例**:
```json
{
  "status": "ok"
}
```

---

### 2. 用户登录管理

#### GET /loginStatus

检查用户登录状态。

**请求参数**:
- `recmtUserName` (string, required): 用户名

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "",
  "result": {
    "status": "0",  // 0: 已登录, 1: 未登录
    "version": 1
  }
}
```

#### GET /login

执行用户登录流程。

**请求参数**:
- `recmtUserName` (string, required): 用户名
- `bnLoginName` (string, required): 登录名

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "登录成功",
  "result": {
    "status": "success",
    "qrCodeUrl": "https://example.com/qr-code.png",  // 二维码URL（如需要）
    "loginMethod": "cookie"  // 登录方式: cookie/qrcode
  }
}
```

---

### 3. 职位管理

#### GET /job/allList

获取所有可用职位列表。

**请求参数**:
- `recmtUserName` (string, required): 用户名

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "职位列表获取成功",
  "result": [
    {
      "jobId": "12345",
      "jobName": "高级Python开发工程师",
      "department": "技术部",
      "location": "北京",
      "status": "active",
      "createTime": "2024-01-01 10:00:00"
    }
  ]
}
```

---

### 4. 候选人筛选

#### GET /jobfilter/trigger

启动候选人筛选任务。

**请求参数**:
- `recmtUserName` (string, required): 用户名
- `bnLoginName` (string, required): 登录名
- `jobId` (string, required): 职位ID
- `batchNo` (string, required): 批次号
- `filterType` (string, optional): 筛选类型，默认为"1"
  - "1": 标准筛选
  - "2": 高级筛选
  - "3": 自定义筛选
- `interval` (string, optional): 间隔时间，默认为"1"
- `endTime` (string, optional): 结束时间

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "筛选任务已启动",
  "result": {
    "taskId": "task-uuid-12345",
    "batchNo": "batch-001",
    "status": "started",
    "estimatedDuration": "30分钟"
  }
}
```

---

### 5. Worker管理

#### GET /worker/status

查询Worker运行状态。

**请求参数**: 无

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "Worker状态查询成功",
  "result": {
    "workerId": "worker-001",
    "status": "running",  // running/idle/paused/error
    "lastHeartbeat": "2024-01-01 10:00:00",
    "currentTask": {
      "taskId": "task-12345",
      "action": "jobFilterTrigger",
      "startTime": "2024-01-01 09:30:00"
    },
    "systemInfo": {
      "cpuUsage": "45%",
      "memoryUsage": "2.1GB",
      "diskUsage": "60%"
    }
  }
}
```

#### POST /worker/admin

发送管理指令给Worker。

**请求参数**:
```json
{
  "command": "pause",  // pause/resume/restart/shutdown
  "reason": "维护升级"
}
```

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "指令已发送",
  "result": {
    "command": "pause",
    "status": "accepted"
  }
}
```

---

### 6. 缓存管理

#### GET /cache/status

查询缓存状态。

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "缓存状态查询成功",
  "result": {
    "totalKeys": 150,
    "memoryUsage": "45MB",
    "hitRate": "85%",
    "categories": {
      "login": 10,
      "jobList": 25,
      "candidates": 115
    }
  }
}
```

#### DELETE /cache/clear

清理缓存。

**请求参数**:
```json
{
  "category": "all",  // all/login/jobList/candidates
  "pattern": "*"      // 可选，匹配模式
}
```

**响应示例**:
```json
{
  "timestamp": "1640995200000",
  "code": 0,
  "message": "缓存清理成功",
  "result": {
    "clearedKeys": 150,
    "category": "all"
  }
}
```

---

## 错误处理

### 错误响应格式

当API调用出现错误时，会返回以下格式的错误响应：

```json
{
  "timestamp": "1640995200000",
  "code": 400,
  "message": "参数错误",
  "result": {
    "status": "error",
    "errorCode": "INVALID_PARAMS",
    "details": {
      "field": "recmtUserName",
      "reason": "用户名不能为空"
    }
  }
}
```

### 常见错误码

- `INVALID_PARAMS`: 参数错误
- `USER_NOT_LOGGED_IN`: 用户未登录
- `TASK_ALREADY_RUNNING`: 任务已在运行
- `WORKER_OFFLINE`: Worker离线
- `BROWSER_ERROR`: 浏览器错误
- `NETWORK_ERROR`: 网络错误

## 使用示例

### Python示例

```python
import requests

# 检查登录状态
response = requests.get(
    "http://localhost:8000/loginStatus",
    params={"recmtUserName": "test_user"}
)
print(response.json())

# 启动筛选任务
response = requests.get(
    "http://localhost:8000/jobfilter/trigger",
    params={
        "recmtUserName": "test_user",
        "bnLoginName": "test_login",
        "jobId": "12345",
        "batchNo": "batch_001"
    }
)
print(response.json())
```

### JavaScript示例

```javascript
// 检查Worker状态
fetch('http://localhost:8000/worker/status')
  .then(response => response.json())
  .then(data => console.log(data));

// 发送管理指令
fetch('http://localhost:8000/worker/admin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    command: 'pause',
    reason: '维护升级'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项

1. **并发限制**: 同一用户同时只能运行一个筛选任务
2. **超时设置**: 长时间运行的任务（如筛选）可能需要30分钟以上
3. **重试机制**: 网络错误时系统会自动重试，客户端无需重复调用
4. **状态同步**: Worker状态更新可能有1-2秒延迟
5. **日志记录**: 所有API调用都会被记录在系统日志中

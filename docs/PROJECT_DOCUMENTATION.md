# SRA智能招聘助手系统 - 项目说明文档

## 项目概述

SRA（Smart Recruiting Assistant）是一个基于Playwright和FastAPI的智能招聘自动化系统，专门用于在Boss直聘等招聘平台上自动化筛选和管理候选人。系统通过模拟人类操作行为，实现自动登录、候选人信息抓取、智能筛选和批量操作等功能。

### 核心特性

- **智能登录系统**: 支持Cookie自动登录和二维码登录
- **候选人信息抓取**: 混合API数据和Canvas渲染文本的智能提取
- **自动化筛选**: 与外部SRAA服务集成的智能候选人评估
- **任务队列管理**: 基于Redis的异步任务处理
- **实时状态监控**: 完整的任务状态跟踪和心跳监控
- **资源自动清理**: 智能的磁盘空间和临时文件管理

## 技术架构

### 技术栈

- **Python 3.11**: 现代Python语法，更好的性能和类型支持
- **FastAPI**: 高性能Web框架，提供RESTful API服务
- **Playwright**: 现代浏览器自动化工具
- **Redis**: 任务队列和状态存储
- **Loguru**: 强大的日志管理系统
- **Pydantic**: 数据验证和序列化

### 系统架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (FastAPI)                     │
├─────────────────────────────────────────────────────────────┤
│                   Service Layer (Flows)                    │
├─────────────────────────────────────────────────────────────┤
│                   Core Layer (Browser/Task)                │
├─────────────────────────────────────────────────────────────┤
│                   Utils Layer (Logger/Cleanup)             │
├─────────────────────────────────────────────────────────────┤
│                Infrastructure (Redis/FileSystem)           │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. API层 (routers/)

#### agent_api.py - 主要业务API
- **登录接口**: `/agent/login` - 处理用户登录流程
- **职位列表**: `/agent/job/list` - 获取可用职位列表
- **筛选触发**: `/agent/jobfilter/trigger` - 启动候选人筛选任务
- **状态查询**: 实时获取任务执行状态

#### ctrl_api.py - 系统控制API
- **Worker状态**: `/worker/status` - 查询Worker运行状态
- **管理指令**: `/worker/admin` - 发送暂停、恢复、重启等指令
- **健康检查**: 系统健康状态监控

#### dispatch_api.py - 任务分发API
- **异步分发**: `/dispatch/async` - 异步任务分发
- **同步分发**: `/dispatch/sync` - 同步任务分发并等待结果

#### api_result.py - 结果管理器
- **结果等待**: 实现任务结果的同步等待机制
- **超时处理**: 智能的超时和重试策略

### 2. 业务流程层 (flows/)

#### login.py - 登录管理
- **Cookie登录**: 自动使用保存的登录状态
- **二维码登录**: 获取二维码URL供用户扫码
- **会话管理**: 自动保存和刷新登录状态
- **登录状态检测**: 智能检测登录失效和账号封禁

#### geek_fetch_flow.py - 候选人抓取
- **页面导航**: 智能导航到推荐页面
- **数据拦截**: 拦截API请求获取候选人数据
- **人类行为模拟**: 模拟真实用户的滚动和点击行为
- **批量操作**: 支持批量收藏和打招呼

#### geek_info_build.py - 信息构建
- **混合数据提取**: 融合API数据和Canvas渲染文本
- **结构化解析**: 提取工作经历、项目经验、教育背景等
- **智能去重**: 处理重复和冗余信息
- **文本重建**: 将Canvas字符重建为连贯文本

#### geek_filter.py - 智能筛选
- **外部服务集成**: 调用SRAA服务进行候选人评估
- **筛选策略**: 支持多种筛选条件和策略
- **结果处理**: 根据筛选结果执行相应操作

#### callback.py - 回调处理
- **状态同步**: 向外部系统同步任务状态
- **结果通知**: 发送任务完成和错误通知
- **登录状态回调**: 同步登录状态变化

### 3. 核心层 (core/)

#### browser_manager.py - 浏览器管理
- **浏览器初始化**: 配置和启动Playwright浏览器
- **反检测设置**: 配置反爬虫检测机制
- **请求拦截**: 设置API请求拦截器
- **Canvas Hook**: 注入JavaScript获取Canvas渲染数据
- **人类行为模拟**: 模拟真实的滚动和点击行为

#### task_processor.py - 任务处理器
- **任务分发**: 根据任务类型分发到对应处理器
- **参数验证**: 验证任务参数的有效性
- **执行跟踪**: 记录任务执行过程和性能指标
- **错误处理**: 统一的异常处理和错误恢复

#### exceptions.py - 异常定义
- **自定义异常**: 定义业务相关的异常类型
- **错误码管理**: 统一的错误码定义和管理

### 4. 工具层 (utils/)

#### logger.py - 日志管理
- **多级日志**: 支持控制台和文件日志
- **自动轮转**: 按日期自动轮转日志文件
- **压缩存储**: 自动压缩历史日志文件
- **错误追踪**: 详细的错误堆栈跟踪

#### tracing_manager.py - 追踪管理
- **操作记录**: 记录浏览器操作的详细trace
- **性能监控**: 监控操作性能和资源使用
- **自动清理**: 智能清理过期的trace文件
- **磁盘优化**: 优化trace文件大小和存储

#### auto_cleanup.py - 自动清理
- **定时清理**: 定时清理临时文件和缓存
- **磁盘监控**: 监控磁盘使用情况
- **智能清理**: 根据磁盘使用情况智能清理

#### retry_handler.py - 重试处理
- **智能重试**: 支持指数退避和线性重试策略
- **异常分类**: 区分可重试和不可重试异常
- **性能统计**: 记录重试成功率和性能指标

### 5. 管理工具 (management/)

#### task_manager.py - 任务管理
- **磁盘使用查询**: 查看系统磁盘使用情况
- **清理操作**: 手动触发各种清理操作
- **紧急清理**: 磁盘空间不足时的紧急清理
- **状态监控**: 监控系统运行状态

## 核心业务流程

### 1. 系统启动流程

1. **初始化阶段**
   - 加载配置文件
   - 初始化日志系统
   - 启动Redis连接
   - 初始化浏览器服务

2. **服务启动阶段**
   - 启动自动清理服务
   - 启动Worker管理器
   - 开始监听任务队列
   - 启动心跳监控

3. **就绪阶段**
   - 更新Worker状态为idle
   - 等待任务请求

### 2. 候选人筛选流程

1. **任务接收**
   - API接收筛选请求
   - 验证请求参数
   - 生成任务ID
   - 发送到Redis队列

2. **登录验证**
   - 检查登录状态
   - Cookie自动登录
   - 二维码登录（如需要）
   - 保存登录状态

3. **页面导航**
   - 导航到推荐页面
   - 选择指定职位
   - 等待页面加载完成

4. **数据抓取**
   - 拦截API请求
   - 获取候选人基础信息
   - 提取Canvas渲染文本
   - 融合多源数据

5. **智能筛选**
   - 发送数据到SRAA服务
   - 获取筛选结果
   - 执行相应操作
   - 记录筛选结果

6. **任务完成**
   - 更新任务状态
   - 发送完成回调
   - 清理资源

## 配置管理

### 主要配置项

- **Redis配置**: 连接URL、队列前缀、超时设置
- **浏览器配置**: 反检测设置、性能优化
- **SRAA服务配置**: 服务地址、回调URL
- **文件路径配置**: 日志、数据、trace文件路径
- **清理配置**: 自动清理策略和阈值

### 环境变量

- `account_name`: 账号名称
- `bn_login_name`: 登录用户名

## 部署和运行

### 环境要求

- Python 3.11+
- Redis服务器
- 足够的磁盘空间（用于trace和日志文件）

### 启动方式

1. **Web服务**: `python src/fast_api.py`
2. **Worker进程**: `python src/celery_worker.py`
3. **任务管理**: `python src/management/task_manager.py`

### 监控和维护

- 通过API查询系统状态
- 监控日志文件
- 定期清理临时文件
- 监控磁盘使用情况

## 安全和稳定性

### 反检测机制

- 模拟真实用户行为
- 随机延迟和停顿
- 固定浏览器环境
- 智能错误恢复

### 错误处理

- 分层异常处理
- 智能重试机制
- 详细错误日志
- 自动错误恢复

### 资源管理

- 自动内存清理
- 磁盘空间监控
- 临时文件清理
- 性能优化

## 扩展性

系统采用模块化设计，支持：

- 新增任务类型
- 扩展筛选策略
- 集成其他招聘平台
- 自定义回调处理
- 增加监控指标

## 总结

SRA系统是一个功能完整、架构清晰的智能招聘自动化解决方案。通过合理的模块划分、强大的错误处理机制和智能的资源管理，系统能够稳定可靠地执行复杂的招聘自动化任务，大大提高招聘效率和质量。

# SRA 部署指南

## 概述

本文档详细介绍了SRA (Smart Recruitment Assistant) 系统的部署方法，包括本地开发环境、生产环境和Docker容器化部署。

## 系统要求

### 硬件要求

- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **磁盘**: 50GB可用空间（推荐SSD）
- **网络**: 稳定的互联网连接

### 软件要求

- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+, macOS 10.15+
- **Python**: 3.11或更高版本
- **Redis**: 6.0或更高版本
- **浏览器**: 系统会自动下载Chromium

## 本地开发环境部署

### 1. 环境准备

#### 安装Python
```bash
# Windows (使用官方安装包)
# 下载并安装 Python 3.11+ from https://python.org

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-pip

# CentOS/RHEL
sudo yum install python311 python311-pip

# macOS (使用Homebrew)
brew install python@3.11
```

#### 安装Redis
```bash
# Windows (使用WSL或下载Windows版本)
# 下载 Redis for Windows from https://github.com/microsoftarchive/redis/releases

# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

### 2. 项目设置

#### 克隆项目
```bash
git clone <repository-url>
cd sra
```

#### 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate

# Linux/macOS
source .venv/bin/activate
```

#### 安装依赖
```bash
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

### 3. 配置设置

#### 环境变量配置
创建 `.env` 文件：
```bash
# Redis配置
SRA_REDIS_URL=redis://localhost:6379

# 日志级别
LOG_LEVEL=INFO

# 浏览器配置
BROWSER_HEADLESS=true

# 工作目录
WORK_DIR=/path/to/sra
```

#### Redis配置
编辑Redis配置文件（通常在 `/etc/redis/redis.conf`）：
```conf
# 绑定地址
bind 127.0.0.1

# 端口
port 6379

# 持久化
save 900 1
save 300 10
save 60 10000

# 内存策略
maxmemory-policy allkeys-lru
```

### 4. 启动服务

#### 启动Redis
```bash
# Linux/macOS
redis-server

# Windows
redis-server.exe
```

#### 启动SRA服务
```bash
# 方法1: 使用提供的脚本 (Windows)
restart_services.bat

# 方法2: 手动启动
# 启动FastAPI服务
python src/fast_api.py

# 启动Worker (新终端)
python src/workers/celery_worker.py
```

## 生产环境部署

### 1. 系统配置

#### 创建专用用户
```bash
sudo useradd -m -s /bin/bash sra
sudo usermod -aG sudo sra
su - sra
```

#### 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3.11-pip redis-server nginx supervisor

# CentOS/RHEL
sudo yum install -y python311 python311-pip redis nginx supervisor
```

### 2. 应用部署

#### 部署代码
```bash
cd /opt
sudo git clone <repository-url> sra
sudo chown -R sra:sra /opt/sra
cd /opt/sra

# 创建虚拟环境
python3.11 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
playwright install chromium
```

#### 配置文件
创建生产环境配置 `/opt/sra/.env.prod`：
```bash
SRA_REDIS_URL=redis://localhost:6379
LOG_LEVEL=INFO
BROWSER_HEADLESS=true
WORK_DIR=/opt/sra
```

### 3. 进程管理

#### Supervisor配置
创建 `/etc/supervisor/conf.d/sra.conf`：
```ini
[group:sra]
programs=sra-api,sra-worker

[program:sra-api]
command=/opt/sra/.venv/bin/python /opt/sra/src/fast_api.py
directory=/opt/sra
user=sra
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/sra/api.log
environment=PYTHONPATH="/opt/sra"

[program:sra-worker]
command=/opt/sra/.venv/bin/python /opt/sra/src/workers/celery_worker.py
directory=/opt/sra
user=sra
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/sra/worker.log
environment=PYTHONPATH="/opt/sra"
```

#### 启动服务
```bash
# 创建日志目录
sudo mkdir -p /var/log/sra
sudo chown sra:sra /var/log/sra

# 重新加载Supervisor配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start sra:*

# 查看状态
sudo supervisorctl status
```

### 4. 反向代理配置

#### Nginx配置
创建 `/etc/nginx/sites-available/sra`：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }

    # 静态文件
    location /static/ {
        alias /opt/sra/static/;
        expires 30d;
    }

    # 日志文件
    access_log /var/log/nginx/sra_access.log;
    error_log /var/log/nginx/sra_error.log;
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/sra /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Docker容器化部署

### 1. Dockerfile

创建 `Dockerfile`：
```dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装Playwright浏览器
RUN playwright install chromium

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "src/fast_api.py"]
```

### 2. Docker Compose

创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  sra-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SRA_REDIS_URL=redis://redis:6379
      - BROWSER_HEADLESS=true
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data

  sra-worker:
    build: .
    command: python src/workers/celery_worker.py
    environment:
      - SRA_REDIS_URL=redis://redis:6379
      - BROWSER_HEADLESS=true
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data

volumes:
  redis_data:
```

### 3. 部署命令

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重新构建
docker-compose build --no-cache
```

## 监控和维护

### 1. 健康检查

```bash
# API健康检查
curl http://localhost:8000/health

# Worker状态检查
curl http://localhost:8000/worker/status
```

### 2. 日志监控

```bash
# 查看API日志
tail -f /var/log/sra/api.log

# 查看Worker日志
tail -f /var/log/sra/worker.log

# 查看系统日志
journalctl -u supervisor -f
```

### 3. 性能监控

```bash
# 系统资源监控
htop

# Redis监控
redis-cli info

# 磁盘使用监控
df -h
```

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   # 检查Playwright安装
   playwright install chromium
   
   # 检查系统依赖
   sudo apt install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   systemctl status redis
   
   # 测试连接
   redis-cli ping
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R sra:sra /opt/sra
   sudo chmod +x /opt/sra/restart_services.bat
   ```

### 日志分析

查看详细错误信息：
```bash
# 应用日志
grep ERROR /var/log/sra/*.log

# 系统日志
journalctl -xe
```

## 安全建议

1. **防火墙配置**
   ```bash
   # 只允许必要端口
   sudo ufw allow 22
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

2. **SSL证书**
   ```bash
   # 使用Let's Encrypt
   sudo certbot --nginx -d your-domain.com
   ```

3. **定期更新**
   ```bash
   # 更新系统包
   sudo apt update && sudo apt upgrade
   
   # 更新Python依赖
   pip install -r requirements.txt --upgrade
   ```

## 备份和恢复

### 数据备份
```bash
# 备份Redis数据
redis-cli BGSAVE

# 备份配置文件
tar -czf sra-config-backup.tar.gz /opt/sra/.env.prod /etc/supervisor/conf.d/sra.conf

# 备份日志
tar -czf sra-logs-backup.tar.gz /var/log/sra/
```

### 数据恢复
```bash
# 恢复Redis数据
cp dump.rdb /var/lib/redis/

# 恢复配置
tar -xzf sra-config-backup.tar.gz -C /
```

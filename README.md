# SRA - 智能招聘助手

SRA (Smart Recruitment Assistant) 是一个基于 Playwright 的高度自动化、分布式的浏览器操作平台。它通过 FastAPI 接收指令，利用 Redis 任务队列驱动无头浏览器执行复杂的招聘流程，如登录、筛选候选人、获取简历详情等，并内置了强大的监控、日志和自动维护机制。

## 核心特性

- **异步架构**: 全面采用 `asyncio` 和 `FastAPI`，实现高并发、非阻塞的 I/O 操作。
- **任务队列**: 使用 Redis 作为消息代理，解耦了任务的接收和执行，支持分布式部署和水平扩展。
- **浏览器自动化**: 基于 Playwright，实现了稳定、高效的浏览器自动化操作，并集成了先进的反检测技术。
- **模块化设计**: 代码结构清晰，分为路由、核心、流程、监控、工具等模块，易于维护和扩展。
- **健壮的监控**: 内置心跳、浏览器状态和磁盘空间监控，通过企业微信机器人实时发送告警。
- **自动化运维**: 包含一键重启服务的批处理脚本和一键注册开机自启动的 PowerShell 脚本，简化了部署和维护。
- **分布式锁预检查**: 通过 Redis 分布式锁，确保每日首个核心任务作为“预检任务”被唯一执行，验证核心流程无误后，其他任务才开始执行，极大提升了系统稳定性。

## 核心架构与流程

系统的工作流程围绕任务的派发、执行和结果回收展开，具体流程如下：

```mermaid
graph TD
    subgraph 外部请求_HTTP
        A[用户/外部系统] -->|API 请求| B(FastAPI 应用);
    end

    subgraph 任务派发层_FastAPI
        B -->|写入任务到 Redis| C{Redis 任务队列};
    end

    subgraph 任务执行层_Celery_Worker
        D[Celery Worker] -->|从 Redis 获取任务| C;
        D --> E{任务处理器};
        E -->|根据 action 选择| F[具体任务处理器];
        F --> G[核心业务流];
        G -->|操作浏览器| H(Playwright 浏览器);
        H -->|执行具体操作| I[登录/筛选/抓取等];
        I -->|设置预检查标志| C;
    end

    subgraph 监控与日志
        J(监控模块) -- 定时检查 --> D;
        J -- 定时检查 --> H;
        K(日志系统) -- 记录所有活动 --> L[日志文件];
        J -- 发送告警 --> M[企业微信机器人];
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style C fill:#f96,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style M fill:#9f9,stroke:#333,stroke-width:2px
```

**流程说明:**
1.  **请求接收**: 外部系统通过调用 `FastAPI` 提供的 API 端点来发起任务。
2.  **任务入队**: FastAPI 接收到请求后，将其封装成一个标准的 JSON 任务，并推入 Redis 的任务队列中。
3.  **Worker 获取任务**: Worker 进程持续监听 Redis 队列，一旦有新任务便立即获取。
4.  **预检与锁定**: 对于核心的 `jobFilterTrigger` 任务，Worker 会先尝试获取一个全局的“预检锁”。只有第一个成功获取锁的 Worker 才能继续执行，其他的则会等待“预检完成”信号。
5.  **执行与标记**: 获取锁的 Worker 在成功执行任务的关键步骤后，会在 Redis 中设置一个“预检完成”标志，并释放锁。
6.  **结果与监控**: 任务执行过程中的所有活动都被日志系统记录。监控模块持续检查 Worker 和浏览器的状态，并在出现异常时通过企业微信机器人发送告警。

## 项目结构

```
src/
├── conf/              # 配置文件模块
├── core/              # 核心功能模块 (浏览器管理, 异常定义, 任务处理)
├── flows/             # 核心业务流程 (登录, 筛选, 数据提取)
├── monitors/          # 监控模块 (浏览器, 磁盘, 心跳)
├── routers/           # API 路由模块 (FastAPI)
├── utils/             # 工具模块 (日志, 邮件, 磁盘清理)
├── workers/           # Worker 入口和主循环
└── fast_api.py        # FastAPI 应用主入口
```

## 本地部署与运行 (Windows)

### 1. 环境设置

- **安装 Python**: 确保已安装 Python 3.9 或更高版本。
- **创建虚拟环境**:
  ```bash
  python -m venv .venv
  ```
- **激活虚拟环境**:
  ```bash
  .venv\Scripts\activate
  ```
- **安装依赖**:
  ```bash
  pip install -r requirements.txt
  ```

### 2. 启动/重启服务

项目提供了一个便捷的重启脚本，可以一键停止现有服务并启动新服务。

- **直接双击** `restart_services.bat` 文件。
- 或者在 **CMD (命令提示符)** 中运行:
  ```bash
  restart_services.bat
  ```
该脚本会自动完成以下操作：
- 停止可能正在运行的旧的 FastAPI 和 Celery 进程。
- 激活虚拟环境。
- 设置 `PYTHONPATH`，确保模块可以被正确找到。
- 在后台启动 FastAPI 服务和 Celery Worker。

## 开机自启动 (Windows)

为了实现无人值守运行，可以执行 `register_autostart.ps1` 脚本（使用 PowerShell 运行）。该脚本会自动创建一个 Windows 任务计划，在系统启动时执行 `restart_services.bat`，从而实现服务的开机自启动。

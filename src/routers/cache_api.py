"""
缓存管理API
提供缓存状态查询和管理功能
"""
import time
from typing import Any

from fastapi import APIRouter
from pydantic import BaseModel

from src.conf.config import CONFIG
from src.utils.cache_manager import cache_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/cache",
    tags=["cache management"]
)


class ApiResponse(BaseModel):
    timestamp: str
    code: int
    message: str
    result: Any | None = None


class CacheInfoResponse(BaseModel):
    key: str
    created_at: str
    expire_time: str
    ttl_minutes: int
    enable_night_mode: bool
    is_expired: bool
    is_night_mode: bool


@router.get("/weekly-report/info", response_model=ApiResponse)
async def get_weekly_report_cache_info():
    """
    获取周报缓存信息
    """
    cache_key = f"weekly_report_{CONFIG.worker_ip}"
    cache_info = cache_manager.get_cache_info(cache_key)
    
    now_ts = str(int(time.time() * 1000))
    
    if cache_info is None:
        return ApiResponse(
            timestamp=now_ts,
            code=404,
            message="周报缓存不存在",
            result=None
        )
    
    return ApiResponse(
        timestamp=now_ts,
        code=0,
        message="获取周报缓存信息成功",
        result=cache_info
    )


@router.delete("/weekly-report", response_model=ApiResponse)
async def clear_weekly_report_cache():
    """
    清除周报缓存
    """
    cache_key = f"weekly_report_{CONFIG.worker_ip}"
    success = cache_manager.clear_cache(cache_key)
    
    now_ts = str(int(time.time() * 1000))
    
    if success:
        logger.info(f"周报缓存已清除: {cache_key}")
        return ApiResponse(
            timestamp=now_ts,
            code=0,
            message="周报缓存清除成功",
            result={"cache_key": cache_key}
        )
    else:
        return ApiResponse(
            timestamp=now_ts,
            code=404,
            message="周报缓存不存在，无需清除",
            result={"cache_key": cache_key}
        )


@router.delete("/all", response_model=ApiResponse)
async def clear_all_cache():
    """
    清除所有缓存
    """
    cache_manager.clear_all_cache()
    
    now_ts = str(int(time.time() * 1000))
    logger.info("所有缓存已清除")
    
    return ApiResponse(
        timestamp=now_ts,
        code=0,
        message="所有缓存清除成功",
        result=None
    )


@router.post("/cleanup", response_model=ApiResponse)
async def cleanup_expired_cache():
    """
    清理过期缓存
    """
    cleaned_count = cache_manager.cleanup_expired_cache()
    
    now_ts = str(int(time.time() * 1000))
    logger.info(f"清理了 {cleaned_count} 个过期缓存")
    
    return ApiResponse(
        timestamp=now_ts,
        code=0,
        message=f"过期缓存清理完成，清理了 {cleaned_count} 个缓存",
        result={"cleaned_count": cleaned_count}
    )


@router.get("/status", response_model=ApiResponse)
async def get_cache_status():
    """
    获取缓存系统状态
    """
    from datetime import datetime
    
    # 获取周报缓存信息
    weekly_report_key = f"weekly_report_{CONFIG.worker_ip}"
    weekly_report_info = cache_manager.get_cache_info(weekly_report_key)
    
    # 检查当前是否为夜间模式
    current_time = datetime.now()
    is_night_mode = cache_manager._is_night_mode(current_time)
    
    # 获取最近一次22点时间
    last_evening_cutoff = cache_manager._get_last_evening_cutoff(current_time)
    
    # 获取下一个8点时间
    next_morning_8am = cache_manager._get_next_morning_8am(current_time)
    
    status_info = {
        "current_time": current_time.isoformat(),
        "is_night_mode": is_night_mode,
        "last_evening_cutoff": last_evening_cutoff.isoformat(),
        "next_morning_8am": next_morning_8am.isoformat(),
        "worker_id": CONFIG.worker_ip,
        "weekly_report_cache": weekly_report_info
    }
    
    now_ts = str(int(time.time() * 1000))
    
    return ApiResponse(
        timestamp=now_ts,
        code=0,
        message="缓存系统状态获取成功",
        result=status_info
    )

# -*- coding: utf-8 -*-

import json

import redis
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

# --- 导入共享配置 ---
from src.conf.config import CONFIG

router = APIRouter(
    prefix="/worker",  # 所有路径都会自动加上 /worker 前缀
    tags=["Worker control"]  # 所有路径都会被分组到这个标签下
)

# 使用同步 Redis 客户端进行操作
# 在大型应用中，你可能会通过依赖注入来管理这个客户端
redis_client = redis.from_url(CONFIG.Redis.URL, decode_responses=True)

# --- Pydantic 模型 ---
# 将模型定义放在使用它们的路由旁边，保持高内聚
class AdminCommand(BaseModel):
    command: str = Field(..., examples=["resume", "restart_browser", "shutdown"])

@router.get("/status")
async def get_worker_status():
    """
    获取 Worker 的当前实时状态，返回 pending/running/finished 结构。
    """
    async_redis_client = await redis.asyncio.from_url(CONFIG.Redis.URL)
    raw = await async_redis_client.get(f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}")
    await async_redis_client.close()

    if not raw:
        raise HTTPException(
            status_code=404,
            detail=f"Worker status not found for user '{CONFIG.worker_ip}'. Is the worker running?"
        )
    status = json.loads(raw)
    return status


@router.post("/command")
async def send_admin_command(cmd: AdminCommand):
    """
    向 Worker 发送管理指令。
    支持指令：shutdown、resume、restart、status等。
    """
    # 推送指令是快速的非阻塞操作，可以使用同步客户端
    redis_client.lpush(f"{CONFIG.Redis.ADMIN_QUEUE_PREFIX}{CONFIG.worker_ip}", cmd.model_dump_json())
    return {"message": f"Command '{cmd.command}' sent to worker for user '{CONFIG.worker_ip}'."}


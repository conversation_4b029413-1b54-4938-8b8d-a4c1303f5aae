"""
Monitors模块
包含所有监控相关的功能
"""

from .browser_monitor import (
    browser_monitor_manager,
    check_browser_status,
    detect_browser_abnormal_closure,
    start_browser_monitor,
    stop_browser_monitor
)

from .heartbeat_monitor import (
    heartbeat_monitor_manager,
    start_heartbeat,
    stop_heartbeat,
    update_worker_status
)

__all__ = [
    "browser_monitor_manager",
    "check_browser_status",
    "detect_browser_abnormal_closure",
    "start_browser_monitor",
    "stop_browser_monitor",
    "heartbeat_monitor_manager",
    "start_heartbeat",
    "stop_heartbeat",
    "update_worker_status"
]

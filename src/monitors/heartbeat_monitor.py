"""
心跳监控模块
负责Worker心跳状态维护和监控
"""
import asyncio
import json
from contextlib import suppress
from datetime import datetime

import redis.asyncio as aioredis

from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 全局变量，用于跟踪任务运行状态
is_task_running = False
heartbeat_task = None  # 心跳任务

# --- 心跳更新函数 ---
async def start_heartbeat(redis: aioredis.Redis, interval: int = 60):
    """
    启动心跳更新任务，定期更新worker状态

    Args:
        redis: Redis连接
        interval: 心跳间隔（秒），默认60秒
    """
    global heartbeat_task, current_task_info

    async def heartbeat_loop():
        while True:
            try:
                await update_worker_status(redis, None)
                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                logger.info("心跳任务被取消")
                break
            except Exception as e:
                logger.error(f"心跳更新异常: {e}")
                await asyncio.sleep(interval)  # 出错后也要等待，避免频繁重试

    # 如果已有心跳任务，先取消
    if heartbeat_task and not heartbeat_task.done():
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            pass

    # 启动新的心跳任务
    heartbeat_task = asyncio.create_task(heartbeat_loop())
    logger.info(f"心跳任务已启动，间隔: {interval}秒")


async def stop_heartbeat():
    """停止心跳更新任务"""
    global heartbeat_task

    if heartbeat_task and not heartbeat_task.done():
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            pass
        logger.info("心跳任务已停止")


# --- 状态更新辅助函数 ---
async def update_worker_status(redis: aioredis.Redis, status: str = None, task_info: dict = None):
    """
    维护 worker_status:{USER_ID} 下的 pending/running/finished 三分组结构。
    """
    key = f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}"
    # 读取现有状态
    try:
        raw = await redis.get(key)
        status_data = json.loads(raw) if raw else {"pending": [], "running": [], "finished": []}
    except Exception:
        status_data = {"pending": [], "running": [], "finished": []}
    task_id = 'N/A'

    # 获取当前状态，如果没有指定新状态则保持原状态
    current_status = status_data.get("status", "idle") if status is None else status

    # 任务状态迁移逻辑
    if status == "processing_task" and task_info:
        status_data = {"pending": [], "running": [], "finished": []}
        task_id = task_info.get("id")
        # running 信息
        running_info = {
            "project": "sra",
            "spider": "sraa-robot-v5",
            "id": task_id,
            "pid": "",
            "start_time": datetime.now().isoformat(),
        }
        status_data["running"].append(running_info)
    elif status == "finished" and task_info:
        # 从 running 移到 finished
        current_status = "idle"
        task_id = task_info.get("id")
        running_task = next((t for t in status_data["running"] if t.get("id") == task_id), None)
        if running_task:
            status_data["running"] = [t for t in status_data["running"] if t.get("id") != task_id]
            finished_info = running_task.copy()
            finished_info["end_time"] = datetime.now().isoformat()
            status_data["finished"].append(finished_info)
    # 其他状态不做迁移，仅更新时间戳

    # 记录 worker 心跳和命令
    state = {
        "status": current_status,
        "current_task_id": task_id,
        "last_update_utc": datetime.now().isoformat(),
        "pending": status_data["pending"],
        "running": status_data["running"],
        "finished": status_data["finished"]
    }

    # 检查状态是否有变化，避免重复日志
    previous_status = status_data.get("status")
    status_changed = previous_status != current_status

    with suppress(Exception):
        await redis.set(key, json.dumps(state))
        # 只在状态变化或重要状态时记录日志
        if status_changed or current_status in ["processing_task", "paused_on_error", "finished"]:
            logger.info(f"[Status Update]: {current_status}")
        else:
            logger.debug(f"[Heartbeat]: {current_status}")  # 心跳更新使用debug级别


class HeartbeatMonitorManager:
    """心跳监控管理器"""
    
    def __init__(self):
        self.heartbeat_task = None
        self.is_monitoring = False
    
    async def start_monitoring(self, redis: aioredis.Redis, interval: int = 30):
        """启动心跳监控"""
        if self.is_monitoring:
            logger.warning("心跳监控已在运行")
            return

        await start_heartbeat(redis, interval)
        self.is_monitoring = True
    
    async def stop_monitoring(self):
        """停止心跳监控"""
        if not self.is_monitoring:
            logger.warning("心跳监控未运行")
            return
            
        await stop_heartbeat()
        self.is_monitoring = False
    
    def get_status(self) -> dict:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "task_running": self.heartbeat_task is not None and not self.heartbeat_task.done() if self.heartbeat_task else False
        }


# 全局心跳监控管理器实例
heartbeat_monitor_manager = HeartbeatMonitorManager()

"""
浏览器监控模块
负责监控浏览器状态和异常检测
"""
import asyncio
import json
import time
from datetime import datetime

import redis.asyncio as aioredis
from playwright.async_api import Page

from src.conf.config import CONFIG
from src.utils.logger import get_logger
from src.utils.mailer import send_monitoring_alert

logger = get_logger(__name__)

# 全局变量
browser_monitor_task = None  # 浏览器监控任务


async def check_browser_status(page: Page) -> dict:
    """
    检查浏览器状态，返回状态信息
    
    Args:
        page: Playwright页面对象
        
    Returns:
        dict: 包含浏览器状态信息的字典
    """
    status_info = {
        "is_connected": False,
        "is_closed": False,
        "context_closed": False,
        "browser_closed": False,
        "page_closed": False,
        "url": "unknown",
        "error_details": ""
    }
    
    try:
        # 检查页面是否关闭
        if page.is_closed():
            status_info["page_closed"] = True
            status_info["error_details"] = "页面已关闭"
            return status_info
            
        # 检查浏览器连接状态
        if not page.context.browser.is_connected():
            status_info["is_connected"] = False
            status_info["error_details"] = "浏览器连接已断开"
            return status_info
            
        # 尝试获取当前URL（作为连接性测试）
        try:
            current_url = page.url
            status_info["url"] = current_url
            status_info["is_connected"] = True
        except Exception as e:
            status_info["is_connected"] = False
            status_info["error_details"] = f"无法获取页面URL: {str(e)}"
            
    except Exception as e:
        status_info["error_details"] = f"浏览器状态检查异常: {str(e)}"
        
    return status_info


async def detect_browser_abnormal_closure(page: Page, account_name: str = None) -> bool:
    """
    检测浏览器是否异常关闭
    
    Args:
        page: Playwright页面对象
        account_name: 账户名称
        
    Returns:
        bool: 是否检测到异常关闭
    """
    status_info = await check_browser_status(page)
    
    # 如果浏览器正常连接，返回False
    if status_info["is_connected"]:
        return False
        
    # 检测到异常关闭，发送告警
    error_details = f"""
SRA Worker 检测到浏览器异常关闭

时间: {datetime.now().isoformat()}
用户ID: {CONFIG.worker_ip}
账户: {account_name or '未知'}

状态信息:
- 页面关闭: {status_info["page_closed"]}
- 浏览器关闭: {status_info["browser_closed"]}
- 上下文关闭: {status_info["context_closed"]}
- 连接状态: {status_info["is_connected"]}
- 当前URL: {status_info["url"]}
- 错误详情: {status_info["error_details"]}

建议操作:
1. 检查网络连接
2. 检查浏览器进程状态
3. 重启Worker服务
"""

    # 发送告警邮件
    try:
        await send_monitoring_alert(
            subject="SRA Worker 浏览器异常关闭告警",
            message=error_details,
            alert_type="browser_abnormal_closure"
        )
        logger.error(f"浏览器异常关闭告警已发送: {status_info['error_details']}")
    except Exception as e:
        logger.error(f"发送浏览器异常关闭告警失败: {e}")

    return True


async def start_browser_monitor(page: Page, redis: aioredis.Redis, interval: int = 30):
    """
    启动浏览器监控
    
    Args:
        page: Playwright页面对象
        redis: Redis连接
        interval: 监控间隔（秒）
    """
    global browser_monitor_task
    
    async def browser_monitor_loop():
        """浏览器监控循环"""
        while True:
            try:
                # 检查浏览器状态
                is_abnormal = await detect_browser_abnormal_closure(page)
                
                if is_abnormal:
                    logger.error("检测到浏览器异常关闭，停止监控")
                    break
                
                # 更新监控状态到Redis
                monitor_info = {
                    "timestamp": time.time(),
                    "status": "monitoring",
                    "page_url": page.url if not page.is_closed() else "unknown"
                }
                
                await redis.setex(
                    f"browser_monitor:{CONFIG.worker_ip}",
                    300,  # 5分钟过期
                    json.dumps(monitor_info)
                )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"浏览器监控异常: {e}")
                await asyncio.sleep(interval)
    
    # 启动监控任务
    browser_monitor_task = asyncio.create_task(browser_monitor_loop())
    logger.info(f"浏览器监控已启动，监控间隔: {interval}秒")


async def stop_browser_monitor():
    """停止浏览器监控"""
    global browser_monitor_task
    
    if browser_monitor_task and not browser_monitor_task.done():
        browser_monitor_task.cancel()
        try:
            await browser_monitor_task
        except asyncio.CancelledError:
            pass
        logger.info("浏览器监控已停止")
    else:
        logger.info("浏览器监控未运行")


class BrowserMonitorManager:
    """浏览器监控管理器"""
    
    def __init__(self):
        self.monitor_task = None
        self.is_monitoring = False
    
    async def start_monitoring(self, page: Page, redis: aioredis.Redis, interval: int = 30):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("浏览器监控已在运行")
            return
            
        await start_browser_monitor(page, redis, interval)
        self.is_monitoring = True
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            logger.warning("浏览器监控未运行")
            return
            
        await stop_browser_monitor()
        self.is_monitoring = False
    
    def get_status(self) -> dict:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "task_running": self.monitor_task is not None and not self.monitor_task.done() if self.monitor_task else False
        }


# 全局监控管理器实例
browser_monitor_manager = BrowserMonitorManager()

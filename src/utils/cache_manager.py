"""
缓存管理器
提供时间敏感的缓存功能，支持夜间模式
"""
import threading
from datetime import datetime, timedelta
from typing import Any, Optional, Dict

from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TimeSensitiveCacheManager:
    """
    时间敏感的缓存管理器
    支持：
    1. 普通缓存（指定过期时间）
    2. 夜间模式缓存（晚上22点到次日8点返回22点前的缓存）
    """
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
    
    def _get_current_time(self) -> datetime:
        """获取当前时间"""
        return datetime.now()
    
    def _is_night_mode(self, current_time: datetime) -> bool:
        """
        判断是否为夜间模式（22:00 - 08:00）
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否为夜间模式
        """
        hour = current_time.hour
        return hour >= 22 or hour < 8
    
    def _get_last_evening_cutoff(self, current_time: datetime) -> datetime:
        """
        获取最近一次晚上22点的时间点

        Args:
            current_time: 当前时间

        Returns:
            datetime: 最近一次晚上22点的时间
        """
        if current_time.hour >= 22:
            # 如果当前时间是今天22点之后，返回今天22点
            return current_time.replace(hour=22, minute=0, second=0, microsecond=0)
        else:
            # 如果当前时间是今天22点之前，返回昨天22点
            yesterday = current_time - timedelta(days=1)
            return yesterday.replace(hour=22, minute=0, second=0, microsecond=0)

    def _get_next_morning_8am(self, current_time: datetime) -> datetime:
        """
        获取下一个早上8点的时间点

        Args:
            current_time: 当前时间

        Returns:
            datetime: 下一个早上8点的时间
        """
        if current_time.hour < 8:
            # 如果当前时间是今天8点之前，返回今天8点
            return current_time.replace(hour=8, minute=0, second=0, microsecond=0)
        else:
            # 如果当前时间是今天8点之后，返回明天8点
            tomorrow = current_time + timedelta(days=1)
            return tomorrow.replace(hour=8, minute=0, second=0, microsecond=0)
    
    def set_cache(self, key: str, value: Any, ttl_minutes: int = 30, enable_night_mode: bool = False) -> None:
        """
        设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl_minutes: 缓存时间（分钟）
            enable_night_mode: 是否启用夜间模式
        """
        current_time = self._get_current_time()
        expire_time = current_time + timedelta(minutes=ttl_minutes)
        
        with self._lock:
            self._cache[key] = {
                'value': value,
                'created_at': current_time,
                'expire_time': expire_time,
                'ttl_minutes': ttl_minutes,
                'enable_night_mode': enable_night_mode
            }
        
        logger.debug(f"缓存已设置: key={key}, ttl={ttl_minutes}分钟, night_mode={enable_night_mode}")
    
    def get_cache(self, key: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值，如果不存在或已过期则返回None
        """
        with self._lock:
            if key not in self._cache:
                logger.debug(f"缓存不存在: key={key}")
                return None
            
            cache_item = self._cache[key]
            current_time = self._get_current_time()
            
            # 检查是否启用夜间模式
            if cache_item.get('enable_night_mode', False):
                return self._get_night_mode_cache(key, cache_item, current_time)
            else:
                return self._get_normal_cache(key, cache_item, current_time)
    
    def _get_normal_cache(self, key: str, cache_item: Dict[str, Any], current_time: datetime) -> Optional[Any]:
        """
        获取普通缓存
        
        Args:
            key: 缓存键
            cache_item: 缓存项
            current_time: 当前时间
            
        Returns:
            Optional[Any]: 缓存值或None
        """
        if current_time > cache_item['expire_time']:
            logger.debug(f"普通缓存已过期: key={key}")
            del self._cache[key]
            return None
        
        logger.debug(f"普通缓存命中: key={key}")
        return cache_item['value']
    
    def _get_night_mode_cache(self, key: str, cache_item: Dict[str, Any], current_time: datetime) -> Optional[Any]:
        """
        获取夜间模式缓存

        夜间模式逻辑：
        1. 在夜间时段（22:00-08:00），优先返回22点前创建的缓存
        2. 如果没有22点前的缓存，则返回夜间时段内创建的缓存
        3. 在白天时段，按正常TTL逻辑处理

        Args:
            key: 缓存键
            cache_item: 缓存项
            current_time: 当前时间

        Returns:
            Optional[Any]: 缓存值或None
        """
        created_at = cache_item['created_at']
        expire_time = cache_item['expire_time']

        # 如果当前是夜间模式（22:00-08:00）
        if self._is_night_mode(current_time):
            last_evening_cutoff = self._get_last_evening_cutoff(current_time)

            # 如果缓存是在最近一次22点之前创建的
            if created_at <= last_evening_cutoff:
                # 在夜间模式下，22点前的缓存一直有效到次日8点
                next_morning_8am = self._get_next_morning_8am(current_time)
                if current_time < next_morning_8am:
                    logger.debug(f"夜间模式缓存命中（22点前创建，夜间有效）: key={key}")
                    return cache_item['value']
                else:
                    logger.debug(f"夜间模式缓存已过期（超过次日8点）: key={key}")
                    del self._cache[key]
                    return None

            # 如果缓存是在最近一次22点之后创建的，按正常TTL处理
            else:
                if current_time <= expire_time:
                    logger.debug(f"夜间模式缓存命中（22点后创建）: key={key}")
                    return cache_item['value']
                else:
                    logger.debug(f"夜间模式缓存已过期（TTL过期）: key={key}")
                    del self._cache[key]
                    return None

        # 如果当前不是夜间模式，按普通缓存逻辑处理
        else:
            return self._get_normal_cache(key, cache_item, current_time)
    
    def clear_cache(self, key: str) -> bool:
        """
        清除指定缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功清除
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"缓存已清除: key={key}")
                return True
            return False
    
    def clear_all_cache(self) -> None:
        """清除所有缓存"""
        with self._lock:
            self._cache.clear()
            logger.debug("所有缓存已清除")
    
    def get_cache_info(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存信息（不返回缓存值）
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Dict[str, Any]]: 缓存信息
        """
        with self._lock:
            if key not in self._cache:
                return None
            
            cache_item = self._cache[key]
            current_time = self._get_current_time()
            
            return {
                'key': key,
                'created_at': cache_item['created_at'].isoformat(),
                'expire_time': cache_item['expire_time'].isoformat(),
                'ttl_minutes': cache_item['ttl_minutes'],
                'enable_night_mode': cache_item.get('enable_night_mode', False),
                'is_expired': current_time > cache_item['expire_time'],
                'is_night_mode': self._is_night_mode(current_time)
            }
    
    def cleanup_expired_cache(self) -> int:
        """
        清理过期缓存
        
        Returns:
            int: 清理的缓存数量
        """
        current_time = self._get_current_time()
        expired_keys = []
        
        with self._lock:
            for key, cache_item in self._cache.items():
                if current_time > cache_item['expire_time']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存")
        
        return len(expired_keys)

    async def set_precheck_flag(self, status: str):
        logger.info(f"正在设置预检标志: {status}")
        try:
            import redis.asyncio as aioredis
            redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)

            # 检查是否持有预检锁（只有持有锁的任务才应该设置标志）
            lock_exists = await redis.get(CONFIG.Crawler.PRECHECK_LOCK_KEY)
            logger.info(f"lock_exists: {lock_exists}")
            if lock_exists:  # 如果锁存在，说明当前任务是预检任务
                precheck_passed = await redis.get(CONFIG.Crawler.PRECHECK_FLAG_KEY)
                logger.info(f"precheck_passed: {precheck_passed}")
                if not precheck_passed:
                    # 使用 pipeline 确保原子性
                    pipe = redis.pipeline()
                    pipe.set(CONFIG.Crawler.PRECHECK_FLAG_KEY, status, ex=86400)
                    pipe.delete(CONFIG.Crawler.PRECHECK_LOCK_KEY)
                    await pipe.execute()
                    logger.info(f"预检任务完成，已设置标志并释放锁。")
                else:
                    # 标志已存在，只释放锁
                    await redis.delete(CONFIG.Crawler.PRECHECK_LOCK_KEY)
                    logger.info(f"🔍 预检查标志已存在，仅释放锁。")

            await redis.close()

        except Exception as e:
            logger.error(f"❌ 设置预检查标志或释放锁时出错: {e}", exc_info=True)
            raise ValueError(f"设置预检查标志或释放锁时出错: {e}")

# 全局缓存管理器实例
cache_manager = TimeSensitiveCacheManager()

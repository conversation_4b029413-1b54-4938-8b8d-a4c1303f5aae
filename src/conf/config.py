# config.py
# Shared configuration for the entire architecture.
import os


class Config:
    # --- User/Worker Identifier ---
    @staticmethod
    def get_unique_worker_id() -> str:
        """
        生成一个稳定、唯一的 Worker ID，以应对多地部署和虚拟化带来的地址冲突问题。
        ID 由 主机名、MAC地址 和一个持久化的UUID 组合后哈希生成。
        """
        import hashlib
        import os
        import socket
        import uuid

        # 1. 定义持久化UUID的文件路径
        uuid_file_path = os.path.join("./data", ".worker_uuid")

        # 2. 读取或生成持久化UUID
        try:
            if os.path.exists(uuid_file_path):
                with open(uuid_file_path, 'r') as f:
                    persistent_uuid = f.read().strip()
            else:
                persistent_uuid = str(uuid.uuid4())
                os.makedirs(os.path.dirname(uuid_file_path), exist_ok=True)
                with open(uuid_file_path, 'w') as f:
                    f.write(persistent_uuid)
        except Exception as e:
            # 如果文件操作失败，使用一个临时的随机UUID作为后备
            persistent_uuid = str(uuid.uuid4())

        # 3. 获取主机名
        hostname = socket.gethostname()

        # 4. 获取MAC地址
        # uuid.getnode() 会返回一个48位的整数，我们将其转换为标准的十六进制格式
        mac_address = ':'.join(f'{((uuid.getnode() >> i) & 0xff):02x}' for i in range(0, 48, 8))

        # 5. 组合并哈希
        combined_string = f"{hostname}-{mac_address}-{persistent_uuid}"
        hashed_id = hashlib.sha256(combined_string.encode('utf-8')).hexdigest()

        # 返回哈希值的前16位作为最终ID，足够唯一且长度适中
        return hashed_id[:16]

    @property
    def worker_ip(self) -> str:
        # 为了保持兼容性，属性名仍为 worker_ip，但调用的是新的唯一ID生成函数
        return self.get_unique_worker_id()

    class Redis:
        URL = os.getenv("SRA_REDIS_URL", "redis://:qMdda0ChDwCgR91Bg9tH@************:6800")
        TASK_QUEUE_PREFIX = "playwright_tasks_"
        ADMIN_QUEUE_PREFIX = "playwright_admin_"
        STATUS_KEY_PREFIX = "worker_status:"
        TASK_LOCK_PREFIX = "task_lock:"
        TASK_LOCK_TIMEOUT_SECONDS = 3600  # 1 hour
        RESULT_CHANNEL_PREFIX = "worker_results_"
        TASK_LOCK_TTL_SECONDS = 7200
        WORKER_HEARTBEAT_TIMEOUT_SECONDS = 180

    class SRAA:
        HOST_PORT = "http://sraa.i2soft.net:8300"
        TASK_FILTER_URL = "/task/filter"
        LOGIN_CALLBACK_URL = "/agent/agent_login_callback"
        SYNC_STATUS_URL = "/agent/syncStatus"
        ERROR_CALLBACK_URL = '/task/agent_error_callback'
        SUCCESS_CALLBACK_URL = '/task/agent_success_callback'
        ACTION_CALLBACK_URL = '/task/agent_action_callback'
        SRAA_MOCK = False

    class BossZhiPin:
        WEBSOCKET = {
            "hostname": "ws.zhipin.com",
            "port": 443,
            "path": "/chatws",
            "topic": "chat"
        }
        API = {
            "recommend_url": "https://www.zhipin.com/web/chat/recommend",
            "api_url_pattern": "/wapi/zpjob/rec/geek/list",
            "joblist_url_pattern": "/wapi/zpjob/job/chatted/jobList",
            "search_joblist_url_pattern": "/wapi/zpjob/job/search/job/list",
            "login_url": "https://www.zhipin.com/web/user/?ka=header-login",
            "chat_url": "https://www.zhipin.com/web/chat/index",
            "job_list": "https://www.zhipin.com/web/chat/job/list",
            "data_recruit": "https://www.zhipin.com/web/chat/data-recruit"
        }

    class FilePaths:
        LOGIN_DATA = "./data/user_{account_name}.json"

    class Crawler:
        PLAYWRIGHT = {
            'browser_type': 'chrome',
            'headless': False
        }
        # 固定用户的基础配置
        ANTI_DETECTION = {
            'simulate_human_behavior': True,
            'stealth_mode': True,
            'fixed_environment': True  # 固定环境模式
        }

        # 固定用户行为模拟参数
        HUMAN_BEHAVIOR = {
            'scroll_pause_probability': 0.2,  # 降低停顿频率
            'page_browse_probability': 0.15  # 降低页面浏览频率
        }

        # 所有asyncio.sleep相关的延迟配置
        DELAYS = {
            # 页面导航相关
            'page_navigation_delay': 0.2,  # 页面导航前延迟
            'page_load_wait': 1.0,  # 页面加载等待时间

            # 弹窗处理相关
            'dialog_reaction_time': 0.8,  # 用户看到弹窗的反应时间
            'dialog_reading_time': 1.5,  # 用户阅读弹窗内容的时间
            'dialog_close_wait': 0.8,  # 弹窗关闭后等待时间

            # 点击操作相关
            'click_hover_delay': 0.3,  # 悬停延迟

            # 滚动相关
            'scroll_delay_min': 0.1,  # 滚动最小延迟
            'scroll_delay_max': 0.3,  # 滚动最大延迟
            'canvas_scroll_delay_min': 0.2,  # Canvas滚动最小延迟
            'canvas_scroll_delay_max': 0.4,  # Canvas滚动最大延迟

            # 单个简历处理时间间隔
            'job_detail_interval': 3,  # 处理间隔 3s

        }

        PRECHECK_FLAG_KEY = "daily_jobFilterTrigger_precheck_passed"
        PRECHECK_LOCK_KEY = "precheck_in_progress_lock"

        # Tracing配置（优化磁盘空间占用）
        TRACING = {
            'enabled': True,                    # 是否启用tracing
            'screenshots': False,               # 禁用截图以减少文件大小
            'snapshots': True,                  # 保留DOM快照
            'sources': False,                   # 禁用源码记录以减少文件大小
            'max_trace_size_mb': 100,           # 单个trace文件最大10MB
            'max_total_size_gb': 1,            # 总trace文件最大1GB
            'cleanup_interval_hours': 2,       # 每6小时清理一次
            'keep_days': 7,                    # 保留7天的trace文件
            'temp_cleanup_enabled': True,      # 启用临时文件清理
            'force_cleanup_threshold': 0.85,   # 达到85%限制时强制清理
        }
    class WeChatBot:
        WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=94bf57fa-68af-42d0-8736-6070c5f6b2d1"
        MOCK = True

    class Header:
        # httpbin.org 是一个非常有用的 HTTP 请求和响应测试服务。
        # /headers 端点会以 JSON 格式返回它收到的请求头。
        HEADERS_TEST_URL = "https://httpbin.org/headers"
        HEADERS_OUTPUT_FILE = "data/browser_headers.json"  # 定义输出文件名


# Instantiate the config object
CONFIG = Config()
import base64
import json
import os
import time

import requests

from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Magic String与默认值集中管理
DEFAULT_STATUS = '0'
DEFAULT_ERROR_STATUS = '16'
DEFAULT_ERROR_REASON = '未知异常中断'
SUCCESS_REASON = '正常结束'

def _post_json(url, data, timeout=(20, 10), log_prefix="", max_retries=3):
    """
    通用POST请求方法，统一日志与异常处理，支持重试机制
    """
    for attempt in range(max_retries):
        try:
            logger.info(f"{log_prefix}请求URL: {url} (尝试 {attempt + 1}/{max_retries})")
            logger.info(f"{log_prefix}请求数据: {data}")

            response = requests.post(url=url, json=data, timeout=timeout)
            response.raise_for_status()  # 检查HTTP状态码

            decoded_response = response.content.decode("UTF-8")
            logger.info(f"{log_prefix}返回: {decoded_response}")
            return decoded_response

        except requests.exceptions.Timeout as e:
            logger.warning(f"{log_prefix}请求超时 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"{log_prefix}请求最终超时失败")
                return None

        except requests.exceptions.RequestException as e:
            logger.warning(f"{log_prefix}请求异常 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"{log_prefix}请求最终失败")
                return None

        except Exception as e:
            logger.error(f"{log_prefix}未知异常: {e}")
            return None

        # 重试前等待
        if attempt < max_retries - 1:
            import time
            time.sleep(2 ** attempt)  # 指数退避

    return None

def sync_login_status(cookies=None, status=DEFAULT_STATUS):
    """
    爬虫状态同步接口
    """
    sync_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.SYNC_STATUS_URL
    cookies_str = json.dumps(cookies)
    cookies_str_b64 = base64.b64encode(cookies_str.encode('utf-8')).decode('utf-8')
    data = {
        "cookie": cookies_str_b64,
        "status": status,
        "bnLoginName": os.environ.get('bn_login_name')
    }
    _post_json(sync_url, data, log_prefix="爬虫状态同步接口")

def login_callback(
    action="",
    externalSystemUsername="",
    batchNo="",
    qrCodeUrl="",
    blockedContext=""
):
    """
    爬虫登录回调接口
    """
    callback_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.LOGIN_CALLBACK_URL
    data = {
        "action": action,
        "externalSystemUsername": externalSystemUsername,
        "recmtUserName": os.environ.get('account_name'),
        "bnLoginName": os.environ.get('bn_login_name'),
        "batchNo": batchNo,
        "qrCodeUrl": qrCodeUrl,
        "blockedContext": blockedContext
    }
    decoded_response = _post_json(callback_url, data, log_prefix="爬虫登录回调接口")
    if decoded_response:
        try:
            result = json.loads(decoded_response)
            logger.info(f"爬虫登录回调接口解析结果: {result}")
            return result
        except Exception as e:
            logger.error(f"爬虫登录回调接口结果解析异常: {e}")
            return None
    return None

def success_callback(batch_no):
    """
    爬虫任务筛选正常结束回调接口
    """
    sync_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.SUCCESS_CALLBACK_URL
    data = {
        "id": batch_no,
        "agentEndReason": SUCCESS_REASON,
        "agentEndTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    }
    _post_json(sync_url, data, log_prefix="爬虫任务筛选正常结束回调接口")

def error_callback(batchNo, reason=None, status=None):
    """
    爬虫任务筛选异常回调接口
    """
    sync_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.ERROR_CALLBACK_URL
    if not status:
        status = DEFAULT_ERROR_STATUS
    if not reason:
        reason = DEFAULT_ERROR_REASON
    data = {
        "id": batchNo,
        "agentEndReason": reason,
        "agentEndTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()),
        "agentStatus": status
    }
    _post_json(sync_url, data, timeout=(20, 60), log_prefix="爬虫任务筛选异常回调接口")


def action_callback(recmt_user_name, bn_login_name, batch_no, candidate_id, batch_hi_id):
    """
    反馈收藏成功回调接口
    """
    sync_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.ACTION_CALLBACK_URL
    data = {
        "recmtUserName": recmt_user_name,
        "bnLoginName": bn_login_name,
        "batchNo": batch_no,
        "candicateId": candidate_id,
        "id": batch_hi_id,
        "action": "favorites"
    }
    _post_json(sync_url, data, timeout=(20, 60), log_prefix="反馈收藏成功回调接口")
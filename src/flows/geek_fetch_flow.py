import asyncio
import json
import os
import random
import time
from typing import Any

from playwright.async_api import Page, Response, Frame

from src.conf.config import CONFIG
from src.core.browser_manager import simulate_human_scroll_div, simulate_canvas_scroll
from src.core.exceptions import TaskException, ErrorCodes
from src.flows.callback import success_callback, error_callback, action_callback
from src.flows.geek_filter import geek_filter
from src.flows.geek_info_build import HybridStructureExtractor, get_geek_info_and_frame, _extract_and_rebuild_text
from src.utils.cache_manager import cache_manager
from src.utils.logger import get_logger
from src.utils.tracing_manager import start_trace, stop_trace, save_error_trace

# ==============================================================================
# 核心抓取与解析逻辑封装
# ==============================================================================

logger = get_logger(__name__)


async def simulate_page_browsing(page: Page):
    """
    模拟固定用户的页面浏览行为（简化版）
    """
    try:
        # 获取页面尺寸
        viewport = page.viewport_size
        if not viewport:
            return

        # 固定用户的简单浏览行为
        actions = 2  # 固定执行2个动作

        for i in range(actions):
            if i == 0:
                # 第一个动作：轻微滚动查看页面
                scroll_delta = random.randint(100, 200)
                await page.mouse.wheel(0, scroll_delta)
                await asyncio.sleep(1.5)
            else:
                # 第二个动作：短暂停顿
                await asyncio.sleep(1.0)

    except Exception as e:
        logger.debug(f"页面浏览模拟出错: {e}")


async def close_dialog(page):
    """
    固定用户的弹窗关闭逻辑（简化版）
    """
    try:
        # 使用配置文件中的用户反应时间
        await asyncio.sleep(CONFIG.Crawler.DELAYS['dialog_reaction_time'])

        # 1、立即下载弹出层；2、不要再错过重要消息
        await page.locator("div[data-type='boss-dialog']").wait_for(timeout=3000)
        has_dialog = True
    except Exception as e:
        has_dialog = False
        logger.info("没有弹出提示框，忽略")

    if has_dialog:
        try:
            # 使用配置文件中的查看时间
            await asyncio.sleep(CONFIG.Crawler.DELAYS['dialog_reading_time'])

            # CSS 定位器
            css_selector = "div.boss-popup__close > i"
            close_button = page.locator(css_selector)
            await close_button.wait_for(timeout=3000)

            # 简单的悬停点击
            await close_button.hover()
            await asyncio.sleep(CONFIG.Crawler.DELAYS['click_hover_delay'])
            await close_button.click()

            # 使用配置文件中的等待时间
            await asyncio.sleep(CONFIG.Crawler.DELAYS['dialog_close_wait'])

        except Exception as e:
            logger.info("没有弹出提示框，忽略")
            logger.info("点击事件有问题，可能是按钮被弹层挡住")

def page_url_check(page):
    """
    检查页面URL是否在预期范围内
    """
    try:
        url = page.url
        logger.info(f'当前浏览器URL为：{url}')

        # 定义允许的URL模式
        allowed_patterns = [
            '/web/chat/job/list',
            '/web/chat/recommend',
            '/web/chat/search',
            '/web/chat/index',
            '/web/chat/interaction',
            '/web/chat/geek/manage',
            'web/chat/data-recruit'
        ]

        # 检查是否匹配任何允许的模式
        for pattern in allowed_patterns:
            if pattern in url:
                return True

        # 检查登录失效
        if '/web/user/?ka=' in url:
            logger.warning('登录状态失效')
            # 安全删除cookie文件
            try:
                account_name = os.environ.get('account_name')
                if account_name:
                    login_file = CONFIG.FilePaths.LOGIN_DATA.format(account_name=account_name)
                    if os.path.exists(login_file):
                        os.remove(login_file)
                        logger.info(f"已删除失效的登录文件: {login_file}")
            except Exception as e:
                logger.error(f"删除登录文件时出错: {e}")

            from src.core.exceptions import LoginException, ErrorCodes
            raise LoginException("登录状态失效", ErrorCodes.LOGIN_SESSION_EXPIRED)

        logger.warning('当前url不在预期范围内，抛出异常，暂停操作......')
        from src.core.exceptions import BrowserException, ErrorCodes
        raise BrowserException(f'当前url不在预期范围内: {url}', ErrorCodes.BROWSER_NAVIGATION_FAILED)

    except Exception as e:
        if isinstance(e, (LoginException, BrowserException)):
            raise
        logger.error(f"页面URL检查时发生错误: {e}")
        from src.core.exceptions import BrowserException, ErrorCodes
        raise BrowserException(f"页面URL检查失败: {str(e)}", ErrorCodes.BROWSER_ERROR)


async def page_goto_and_check(url, page: Page):
    """
    固定用户的页面导航函数（简化版）
    """
    # 使用配置文件中的导航延迟
    await asyncio.sleep(CONFIG.Crawler.DELAYS['page_navigation_delay'])

    await page.goto(url)

    # 使用配置文件中的页面加载等待时间
    await asyncio.sleep(CONFIG.Crawler.DELAYS['page_load_wait'])

    page_url_check(page)
    await close_dialog(page)

    # 偶尔进行简单的页面浏览（降低频率）
    # if random.random() < CONFIG.Crawler.HUMAN_BEHAVIOR['page_browse_probability']:
    #     await simulate_page_browsing(page)


# @retry_async(
#     config=RetryConfig(max_attempts=3, base_delay=2.0, exponential_base=1.5),
#     on_retry=lambda attempt, exc, delay: logger.warning(f"详情页处理重试 {attempt}: {exc}")
# )
async def process_detail_page(page: Page, geek_api_data: dict):
    """
    增强的详情页处理逻辑，包含重试机制和更好的错误处理
    """
    geek_card = geek_api_data.get("geekCard", geek_api_data)
    geek_name = geek_card.get("geekName", "未知候选人")

    # 记录开始时间
    start_time = time.time()

    try:
        logger.info(f"开始处理详情页: {geek_name}")

        # 1. 获取简历文本和【正确的Frame上下文】
        resume_frame, resume_summary_html_content = await get_geek_info_and_frame(page)

        if not resume_frame:
            logger.error(f"未能获取 {geek_name} 的简历iframe，将仅使用API数据（无文本）。")
            # 即使没有文本，我们依然可以解析API数据
            extractor = HybridStructureExtractor("", geek_api_data)
            structured_result = extractor.parse()
            logger.info(f"成功发送 {geek_name} 的纯API数据。")
            return

        # 滚动简历信息 Canvas 到底部
        await simulate_canvas_scroll(page, resume_frame)

        # 滚动后，只调用轻量级的文本提取函数
        logger.info("滚动完成，重新提取Canvas文本以获取懒加载内容...")
        final_text_after_scroll = await _extract_and_rebuild_text(resume_frame, geek_name)

        # 使用滚动后的文本，如果失败则退回到滚动前的文本
        final_text = final_text_after_scroll
        print("================ final_text ================")
        logger.info(final_text)
        geek_api_data_json = json.dumps(geek_api_data, indent=4, ensure_ascii=False)
        logger.info("================ geek_api_data ================:\n{data}", data=geek_api_data_json)
        logger.info("================ resume_summary_html_content ================:\n{data}", data=resume_summary_html_content)

        # 4. 使用混合解析器进行数据融合
        # 注意：传入的是顶层的 geek_api_data，以便 HybridExtractor 能访问到 geekCard
        extractor = HybridStructureExtractor(final_text, geek_api_data, resume_summary_html_content)
        structured_result = extractor.parse()

        # 5. 将结果放入处理队列
        structured_result_json = json.dumps(structured_result, indent=4, ensure_ascii=False)
        logger.info("============ structured_result ============\n{data}", data=structured_result_json)
        logger.info(f"成功解析并获取 {geek_name} 的融合数据。")

        # 记录处理完成时间和耗时
        end_time = time.time()
        processing_time = end_time - start_time
        logger.info(f"简历处理完成: {geek_name}, 耗时: {processing_time:.2f}秒")

        return structured_result_json
    except Exception as e:
        # 记录错误时的耗时
        end_time = time.time()
        processing_time = end_time - start_time
        logger.error(f"处理详情页 {geek_name} 时发生错误: {e}, 耗时: {processing_time:.2f}秒", exc_info=True)
        raise ValueError(f'简历详情获取处理异常: {e}')


async def simulate_human_click(page: Page, element_locator, delay_before=None, delay_after=None):
    """
    模拟固定用户的点击行为（简化版）
    """
    try:
        # 固定的点击前延迟
        if delay_before:
            await asyncio.sleep(delay_before[0])  # 使用范围的最小值
        else:
            await asyncio.sleep(0.5)

        # 简单的悬停点击
        await element_locator.hover()
        await asyncio.sleep(0.3)
        await element_locator.click()

        # 固定的点击后延迟
        if delay_after:
            await asyncio.sleep(delay_after[0])  # 使用范围的最小值
        else:
            await asyncio.sleep(0.4)

    except Exception as e:
        logger.error(f"模拟点击失败: {e}")
        raise


async def click_next_button_in_iframe(page: Page):
    """
    增强的iframe中"下一页"按钮点击，添加更多人类行为特征

    :param page: Playwright 的 Page 对象。
    :return: 如果成功点击则返回 True，否则返回 False。
    """
    try:
        # 添加随机延迟，模拟用户思考时间
        await asyncio.sleep(random.uniform(1.0, 2.0))

        # 1. 使用 FrameLocator 获取 iframe 的上下文，这是最推荐的方式
        recommend_frame: Frame = page.frame(name="recommendFrame")
        if not recommend_frame:
            await page.wait_for_selector('iframe[name="recommendFrame"]', timeout=10000)
            recommend_frame = page.frame(name="recommendFrame")
        if not recommend_frame:
            raise Exception("未能找到外部 recommendFrame")

        # 2. 在 iframe 内部定位 "next" 按钮
        # 我们使用类名来定位，这比依赖 data-v-* 属性稳定得多
        next_button_selector = 'div.turn-btn.next'
        logger.info(f"在 iframe 内定位按钮: {next_button_selector}")
        next_button = recommend_frame.locator(next_button_selector)
        logger.info("“下一页”按钮已找。")

        if await next_button.is_enabled():
            logger.info("按钮可用，正在执行点击操作...")

            # 固定用户的查看时间（熟练用户）
            await asyncio.sleep(1.5)

            # 使用简化的点击模拟
            await simulate_human_click(
                page,
                next_button,
                delay_before=(0.5,),
                delay_after=(1.0,)
            )

            # 4. 点击后等待，让新内容有时间加载
            logger.info("点击完成，等待加载新简历 2.0s ...")
            await asyncio.sleep(2.0)

            return True
        else:
            logger.warning("“下一页”按钮存在但不可用（可能已是最后一页）。")
            return False

    except Exception as e:
        logger.error(f"点击“下一页”按钮时发生错误: {e}")
        raise ValueError(f"点击“下一页”按钮时发生错误: {e}")


async def select_job(
        page: Page,
        job_id: str,
        filter_type: 1,
        batch_no: str
):
    """
    使用 Playwright 在下拉菜单中选择一个指定的职位。

    Args:
        page: Playwright 的 Page 对象。
        job_id: 要选择的目标职位的 value 属性值。
        filter_type: 筛选类型。
        timeout_seconds: 操作的超时时间（秒）。

    Returns:
        bool: 操作是否成功。
    """

    try:
        logger.info(f"开始选择职位，Job ID: {job_id}, Filter Type: {filter_type}")

        # 1. 根据 filterType 确定定位器
        # Playwright 支持更简洁的 CSS 选择器，但我们也可以继续使用 XPath
        if filter_type in ["2", "3"]:
            dropdown_label_selector = "//div[@class='page-interaction']//div[@class='ui-dropmenu-label']"
            # 使用 f-string 和引号的正确组合来构建 XPath
            option_item_selector = f'li.job-item[value="{job_id}"]'
        else:
            dropdown_label_selector = "#headerWrap > div > div > div.ui-dropmenu.ui-dropmenu-label-arrow.ui-dropmenu-drop-arrow.job-selecter-wrap"
            option_item_selector = f'li.job-item[value="{job_id}"]'

        # 2. 点击下拉菜单标签以展开列表
        recommend_frame: Frame = page.frame(name="recommendFrame")
        if not recommend_frame:
            await page.wait_for_selector('iframe[name="recommendFrame"]', timeout=5000)
            recommend_frame = page.frame(name="recommendFrame")
        if not recommend_frame: raise Exception("未能找到外部 recommendFrame")

        logger.info("正在点击下拉菜单...")
        await recommend_frame.click(dropdown_label_selector)
        logger.info("下拉菜单已点击。")

        await asyncio.sleep(2)
        # 3. 定位并点击目标选项
        # locator() 是懒加载的，它在操作时才去查找元素
        option_item = recommend_frame.locator(option_item_selector)
        logger.info(f"正在查找并点击职位选项: {option_item_selector}")

        # Playwright 的 click 会自动滚动到视图并等待元素可交互
        # 这一个动作就替代了 Selenium 的 WebDriverWait, find_element, scrollIntoView, click 和多种异常处理
        await recommend_frame.click(option_item_selector)

        logger.info(f"成功选择职位 ID: {job_id}")
        return True
    except Exception as e:
        # Playwright 的 TimeoutError 是最常见的异常，它涵盖了元素未找到、不可见、不可交互等多种情况
        error_message = f"任务职位选择异常。Job ID: {job_id}"
        logger.error(error_message)
        error_callback(batchNo=batch_no, reason='任务职位选择异常', status='13')
        return False


def check_end_time(batch_no, end_time):
    # 检查结束时间
    if end_time:
        try:
            end_time_sec = time.mktime(time.strptime(end_time, "%Y-%m-%d%H:%M:%S"))
            now = time.time()
            if now > end_time_sec:
                logger.info('当前时间已超过任务结束时间，结束任务')
                error_callback(batch_no, '无候选人数据，已重新筛选3次，不允许操作中断', '8')
                return None
        except ValueError as e:
            raise TaskException(
                f"结束时间格式错误: {end_time}",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"end_time": end_time, "error": str(e)}
            )
    return None


# @retry_async(
#     config=RetryConfig(max_attempts=3, base_delay=3.0, exponential_base=2.0),
#     on_retry=lambda attempt, exc, delay: logger.warning(f"推荐牛人获取重试 {attempt}: {exc}")
# )
class ResponseListenerManager:
    """响应监听器管理器，确保监听器的正确添加和清理"""

    def __init__(self, page):
        self.page = page
        self.handler = None
        self.is_active = False

    def add_listener(self, handler):
        """添加响应监听器"""
        if self.is_active and self.handler:
            # 如果已有监听器，先清理
            self.remove_listener()

        self.handler = handler
        self.page.on("response", handler)
        self.is_active = True
        logger.debug("已添加响应监听器")

    def remove_listener(self):
        """移除响应监听器"""
        if self.is_active and self.handler:
            try:
                self.page.remove_listener("response", self.handler)
                logger.debug("已移除响应监听器")
            except Exception as e:
                logger.debug(f"移除监听器时出错: {e}")
            finally:
                self.handler = None
                self.is_active = False

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.remove_listener()

# 全局变量用于存储当前的响应监听器管理器
_listener_manager = None

async def fetch_recommended_geeks(
        job_id: str,
        job_name: str,
        filter_type: int,
        batchNo,
        page: Page,
        endTime = '',
        account_name: str = None,
        job_detail: dict = None
):
    """
    主抓取函数：负责列表页的API数据获取和导航控制。
    """
    global _listener_manager

    check_end_time(batchNo, endTime)

    try:
        # 启动特定操作的tracing记录
        await start_trace(page, "fetch_geeks", account_name)

        recommend_url = CONFIG.BossZhiPin.API['recommend_url']
        api_url_pattern = CONFIG.BossZhiPin.API['api_url_pattern']
        joblist_url_pattern = CONFIG.BossZhiPin.API['joblist_url_pattern']
        search_joblist_url_pattern = CONFIG.BossZhiPin.API['search_joblist_url_pattern']
        all_geeks_api_data = []
        api_response_future = None

        async def handle_response(response: Response):
            nonlocal all_geeks_api_data, api_response_future
            if api_url_pattern in response.url and response.request.method == 'GET':
                try:
                    data = await response.json()
                    if data.get("code") == 0 and "zpData" in data:
                        zp_data = data["zpData"]
                        geek_list = zp_data.get("geekList", [])
                        has_more = zp_data.get("hasMore", False)
                        logger.info(f"接口返回 {len(geek_list)} 个候选人, hasMore: {has_more}")

                        current_ids = {g['encryptGeekId'] for g in all_geeks_api_data}
                        new_geeks = [g for g in geek_list if g['encryptGeekId'] not in current_ids]
                        all_geeks_api_data.extend(new_geeks)
                        if api_response_future and not api_response_future.done():
                            api_response_future.set_result(has_more)
                    else:
                        if api_response_future and not api_response_future.done():
                            api_response_future.set_result(False)
                except Exception as e:
                    logger.error(f"处理API响应时出错: {e}")
                    if api_response_future and not api_response_future.done():
                        api_response_future.set_result(False)
            if joblist_url_pattern in response.url and response.request.method == 'GET':
                logger.info(f"获取 Joblist {response.url}")
                try:
                    data = await response.json()
                    if data.get("code") == 0 and "zpData" in data:
                        zp_data = data["zpData"]
                        logger.debug(zp_data)
                        logger.debug(f"原始职位数量: {len(zp_data)}")
                        # 使用列表推导式筛选出 jobOnlineStatus 为 1 的职位
                        online_jobs = [job for job in zp_data if job.get('jobOnlineStatus') == 1]
                        logger.debug(f"筛选后（在线）的职位数量: {len(online_jobs)}")
                        logger.debug("筛选出的职位详情:")
                        for job in online_jobs:
                            logger.info(job['encryptJobId'] + " " + job['jobName'] + " " + job['description'])
                    else:
                        logger.error(f"API响应为空，具体信息：{data}")
                except Exception as e:
                    logger.error(f"处理API响应时出错: {e}")
            if search_joblist_url_pattern in response.url and response.request.method == 'GET':
                logger.debug(f"获取 search Joblist {response.url}")
                try:
                    data = await response.json()
                    if data.get("code") == 0 and "zpData" in data:
                        zp_data = data["zpData"]
                        logger.info(zp_data)
                        logger.debug(f"原始职位数量: {len(zp_data)}")
                        job_status = False
                        for job in zp_data:
                            logger.info(job)
                            if job_id == job['jid']:
                                logger.debug('当前职位状态正常')
                                job_status = True
                        if not job_status:
                            raise ValueError("职位不存在")
                    else:
                        logger.error(f"API响应为空，具体信息：{data}")
                except Exception as e:
                    logger.error(f"处理API响应时出错: {e}")

        # 初始化或重用监听器管理器
        if not _listener_manager:
            _listener_manager = ResponseListenerManager(page)

        # 添加响应监听器
        _listener_manager.add_listener(handle_response)

        logger.info(f"正在导航至推荐牛人页面: {recommend_url}")
        await page_goto_and_check(recommend_url, page)
        await asyncio.sleep(2)
        await close_dialog(page)

        try:
            frame_handle = await page.locator('iframe[name="recommendFrame"]').element_handle()
            iframe: Frame = await frame_handle.content_frame()
            await iframe.wait_for_selector('.card-item', timeout=20000)
        except Exception as e:
            logger.error(f"初始页面加载或渲染失败: {e}", exc_info=True)
            await page.screenshot(path="initial_load_failed.png", full_page=True)

            # 清理监听器（初始加载失败时）
            if _listener_manager:
                _listener_manager.remove_listener()
            return None

        recommend_frame: Frame = page.frame(name="recommendFrame")
        first_li = recommend_frame.locator("ul.job-list li").first
        value = await first_li.get_attribute("value")
        if value == job_id:
            logger.info("job_id 匹配第一个")
        else:
            logger.info("job_id 不匹配第一个")
            all_geeks_api_data = []
            job_selected = await select_job(page, job_id, filter_type, batch_no=batchNo)
            if not job_selected:
                logger.info("选择职位失败，终止处理。")

                # 清理监听器（选择职位失败时）
                if _listener_manager:
                    _listener_manager.remove_listener()
                return None

        await asyncio.sleep(2)

        # 循环加载所有剩余的API数据
        has_more_data = True
        page_num = 2
        max_pages = 4
        current_scroll = 0
        while has_more_data and page_num <= max_pages:
            logger.info(f"--- 正在加载第 {page_num} 页API数据 ---")
            api_response_future = asyncio.get_running_loop().create_future()
            current_scroll = await simulate_human_scroll_div(page, iframe, current_scroll)
            try:
                has_more_data = await asyncio.wait_for(api_response_future, timeout=20)
            except asyncio.TimeoutError:
                has_more_data = False
            page_num += 1

        logger.info(f"API数据加载完成，共捕获 {len(all_geeks_api_data)} 个候选人信息。")

        # status：3 批量打招呼 2 收藏，1:打招呼 ，0 不匹配, '':重复
        success = False
        process = 0
        matchprocess = 0

        # 逐个处理已捕获的候选人
        for index, geek_api_data in enumerate(all_geeks_api_data):
            if index != 0:
                await click_next_button_in_iframe(page)
            else:
                await _open_resume_by_candidate_id(page, geek_api_data.get("encryptGeekId"))

            geek_card = geek_api_data.get("geekCard", {})
            geek_name = geek_card.get("geekName", "未知候选人")
            viewed = geek_card.get("viewed")
            if viewed:
                logger.info(f'{geek_name}简历已看过，5秒后跳过')
                await asyncio.sleep(5)
                continue

            resume_status = await is_resume_closed(page)
            if resume_status:
                continue

            # 处理简历
            structured_result_json = await process_detail_page(page, geek_api_data)
            isLastPage = False
            if index == len(all_geeks_api_data) - 1:  # 修复：应该是最后一个元素
                isLastPage = True
            status, isContinue, batchHiCandidates, batchHiIds = await geek_filter(
                batchNo, json.loads(structured_result_json).get("candidate"), job_detail=job_detail, isLastPage=isLastPage)

            # 更新计数器
            process += 1

            # 预检查成功，设置标志并释放锁（只在第一个候选人处理完成后执行一次）
            if index == 0:  # 只在处理第一个候选人时执行
                cache_manager.set_precheck_flag("1")

            # 根据geek_filter返回的状态执行对应操作
            # 使用geek_filter内部已处理的逻辑，避免重复判断
            if status == '1' and isContinue == '0':
                # 打完招呼，就结束
                success = True
                logger.info("执行打招呼结束操作")
            elif status == '2' and isContinue == '0':
                # 收藏后，就停止
                await like_action(page)
                success = True
            elif status == '2' and isContinue == '1':
                # 收藏后继续
                await like_action(page)
            elif status == '3' and isContinue == '1':
                # 批量打招呼，并且下一个
                await batch_collect_candidates(page, batchNo, all_geeks_api_data, batchHiCandidates, batchHiIds)
                break
            elif status == '3' and isContinue == '0':
                # 批量打完招呼，就结束
                await batch_collect_candidates(page, batchNo, all_geeks_api_data, batchHiCandidates, batchHiIds)
                success = True
            elif status == '0' and isContinue == '0':
                # 不匹配且停止
                success = True
                
            # 检查是否成功完成
            if success:
                logger.info(f'达到正常结束的条件了,isContinue:{isContinue},status:{status}')

                # 清理监听器（成功结束时）
                if _listener_manager:
                    _listener_manager.remove_listener()

                await stop_trace(page)
                success_callback(batch_no = batchNo)
                return True

            if endTime != '':
                endTimeSec = time.mktime(time.strptime(endTime, "%Y-%m-%d%H:%M:%S"))
                now = time.time()
                if now < endTimeSec:
                    logger.info('当前时间并未超过规定时间，继续筛选')
                else:
                    logger.info('当前时间已超过任务结束时间，结束任务')
                    error_callback(batchNo, '无候选人数据，已重新筛选3次，不允许操作中断', '8')
                    return True

            await page.wait_for_selector('iframe[name="recommendFrame"]', timeout=5000)
            # 等待后继续处理
            logger.info(f"处理完进行等待 {CONFIG.Crawler.DELAYS['job_detail_interval']}s ......")
            await asyncio.sleep(CONFIG.Crawler.DELAYS['job_detail_interval'])

        # 清理当前监听器
        if _listener_manager:
            _listener_manager.remove_listener()


        await fetch_recommended_geeks(job_id,
                                      job_name,
                                      filter_type,
                                      batchNo,
                                      page=page,
                                      endTime=endTime,
                                      account_name=account_name,
                                      job_detail=job_detail)
        return None

    except Exception as e:
        # 清理监听器（异常情况下）
        if _listener_manager:
            _listener_manager.remove_listener()

        # 标记任务失败并保存错误trace
        error_msg = f"任务执行失败: {str(e)}"
        await save_error_trace(page, "fetch_geeks_error", account_name)
        logger.error(error_msg, exc_info=True)
        raise

async def is_resume_closed(page):
    resume_frame: Frame = page.frame(name="recommendFrame")
    try:
        resume_dialog = resume_frame.locator("div.resume-tip")
        text = await resume_dialog.all_inner_texts()
        logger.info(f"简历已关闭弹窗: {text}")
        return text[0].strip() == "该用户已关闭简历"
    except Exception:
        return False


async def like_action(page):
    resume_frame: Frame = page.frame(name="recommendFrame")
    if not resume_frame:
        await page.wait_for_selector('iframe[name="recommendFrame"]', timeout=5000)
        resume_frame = page.frame(name="recommendFrame")
    if not resume_frame: raise Exception("未能找到外部 recommendFrame")
    # 判断是否已经收藏
    try:
        # 未收藏
        await resume_frame.wait_for_selector('div.like-icon-and-text', timeout=5000)
        await resume_frame.click('div.like-icon-and-text')
        logger.info("收藏成功")
        return True
    except Exception as e:
        logger.error("已收藏过，无需收藏，直接下一个")
        return True

## 批量收藏代码

async def _close_resume_detail(page: Page):
    """
    关闭弹出简历详情页。
    """
    close_btn_xpath = "//*[@class='boss-popup__close']"

    resume_frame: Frame = page.frame(name="recommendFrame")
    await resume_frame.wait_for_selector(close_btn_xpath, timeout=5000)

    try:
        await resume_frame.click(close_btn_xpath)
    except Exception as e:
        raise ValueError(f"关闭弹窗失败: {e}")

    return True


async def _open_resume_by_candidate_id(page: Page, candidate_id: str):
    list_page_iframe = page.frame(name="recommendFrame")
    if not list_page_iframe:
        logger.error("无法重新定位列表页iframe，终止处理。")
        return None

    card_selector = f'div.card-inner[data-geekid="{candidate_id}"]'
    card_locator = list_page_iframe.locator(card_selector)

    try:
        await card_locator.scroll_into_view_if_needed()
        await card_locator.click()
        await page.wait_for_load_state('domcontentloaded')

    except Exception as e:
        logger.error(f"处理候选人详情时出错: {e}")
        raise ValueError(f"处理候选人详情时出错: {e}")


async def batch_collect_candidates(page: Page, batch_no: str, geek_list: list[Any],
                                   batch_candidates: list[str], batch_ids: list[str]) -> bool:
    """
    批量收藏候选人
    """
    logger.info("************批量收藏开始***********")
    await _close_resume_detail(page)
    for index, geek_api_data in enumerate(geek_list):

        geek_card = geek_api_data.get("geekCard", geek_api_data)
        geek_name = geek_card.get("geekName", "未知候选人")

        candidate_id = geek_api_data.get("encryptGeekId")
        if candidate_id and candidate_id in batch_candidates:
            logger.info(f"候选人{geek_name} {candidate_id} 在批量收藏列表中，准备操作。")
            await _open_resume_by_candidate_id(page, candidate_id)
            await like_action(page)
            await _close_resume_detail(page)
            index = batch_candidates.index(candidate_id)
            action_callback(geek_name, os.environ.get('bn_login_name'), batch_no, candidate_id, batch_ids[index])

    logger.info("************批量收藏结束***********")
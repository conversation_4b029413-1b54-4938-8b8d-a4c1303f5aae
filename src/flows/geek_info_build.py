# -*- coding: utf-8 -*-
import re
from typing import Optional, List, Dict, Any

import jieba
import numpy as np
from bs4 import BeautifulSoup
from playwright.async_api import Page, Frame

from src.utils.logger import get_logger

logger = get_logger(__name__)


# ------------------------------------------------------------------------------
# V12 无状态名字锚点提取器 (最终版)
# ------------------------------------------------------------------------------

class StatelessNameAnchorExtractor:
    """
    V12 (Parameterized Name-Anchor): 最终的、由外部知识引导的无状态提取器。
    它接收一个确切的名字作为锚点，在混合事件流中找到该锚点的首次出现，
    并以此为基准精确切分出最新一份简历的数据。
    """

    def __init__(self, anchor_name: str):
        self.anchor_normalized = re.sub(r'\s+', '', anchor_name) if anchor_name else ""

    def get_latest_resume_events(self, all_events: List[Dict]) -> List[Dict]:
        """从混合事件流中，提取出属于最后一份简历的事件。"""
        if not all_events or not self.anchor_normalized:
            return all_events

        # 1. 逐步构建规范化的文本，并实时查找锚点
        current_text_normalized = ""
        for i, event in enumerate(all_events):
            if event.get('type') == 'fill' and event.get('text'):
                event_text_normalized = re.sub(r'\s+', '', event.get('text'))

                # 优化查找：只有当新拼接的文本块可能包含锚点时才查找
                newly_formed_text = current_text_normalized[-len(self.anchor_normalized):] + event_text_normalized
                current_text_normalized += event_text_normalized

                found_pos = newly_formed_text.find(self.anchor_normalized)

                if found_pos != -1:
                    # 找到了！现在需要确定这个名字是从哪个事件开始的
                    # 这是一个简化的、但足够健壮的回溯逻辑
                    search_buffer = ""
                    for j in range(i, -1, -1):
                        search_event = all_events[j]
                        if search_event.get('type') == 'fill' and search_event.get('text'):
                            search_buffer = re.sub(r'\s+', '', search_event.get('text')) + search_buffer
                            if self.anchor_normalized in search_buffer:
                                split_index = j
                                logger.info(
                                    f"Found name anchor '{self.anchor_normalized}' starting at event index {split_index}.")
                                return all_events[split_index:]

        logger.warning(f"Name anchor '{self.anchor_normalized}' not found. Returning all events.")
        return all_events

# ------------------------------------------------------------------------------
# V19 文本重构引擎 (全新“样式感知”模型)
# ------------------------------------------------------------------------------

def is_alphanumeric(char: str) -> bool:
    """辅助函数：检查一个字符是否是英文字母或数字。"""
    char = char.strip()
    if not char: return False
    return 'a' <= char.lower() <= 'z' or '0' <= char <= '9'

def _preprocess_events_for_layout(events: List[Dict]) -> List[Dict]:
    """计算每个文本块的有效边界框(effective_x, effective_r)"""
    for event in events:
        x, width = event.get('x', 0), event.get('width', 0)
        text_align = event.get('style', {}).get('textAlign', 'left')
        if text_align in ('right', 'end'):
            effective_x = x - width
        elif text_align == 'center':
            effective_x = x - (width / 2.0)
        else:
            effective_x = x
        event['effective_x'] = effective_x
        event['effective_r'] = effective_x + width
    return events


def _reconstruct_lines_from_events(line_events: List[Dict]) -> str:
    """从一行事件中，根据间距拼接出文本"""
    line_events.sort(key=lambda e: e.get('effective_x', 0))
    line_text = ""
    if line_events:
        line_text = line_events[0].get('text', '')
        for i in range(1, len(line_events)):
            prev_r = line_events[i - 1].get('effective_r', 0)
            curr_l = line_events[i].get('effective_x', 0)
            # 如果两个词块之间有超过4px的物理空隙，就加一个空格
            if curr_l - prev_r > 4:
                line_text += ' '
            line_text += line_events[i].get('text', '')
    return line_text


def _join_lines_semantically(processed_lines: List[Dict]) -> str:
    """
    【V22.2 修复版】
    恢复了之前版本中处理自动换行时，用于智能判断中英文连接的 `jieba` 逻辑。
    """
    if not processed_lines: return ""

    canvas_width = max(line['end_x'] for line in processed_lines) if processed_lines else 750
    line_y_gaps = [processed_lines[i]['y'] - processed_lines[i - 1]['y'] for i in range(1, len(processed_lines))]
    median_line_height = np.median(line_y_gaps) if line_y_gaps else 20

    CONNECTING_PUNCTUATION = (',', '、', '，')
    ENDING_PUNCTUATION = ('。', '？', '！', '.', ';', '；', ':', '：')

    final_output = processed_lines[0]['text']
    for i in range(1, len(processed_lines)):
        prev_line, curr_line = processed_lines[i-1], processed_lines[i]
        vertical_gap = curr_line['y'] - prev_line['y']
        clean_curr_text = curr_line['text'].strip()

        if not clean_curr_text: continue

        if clean_curr_text.startswith(CONNECTING_PUNCTUATION):
            final_output += clean_curr_text
            continue

        prev_text_stripped = prev_line['text'].strip()
        date_range_pattern = r"^\S.*?([\d.]{4,}\.\d{1,2})\s*-\s*([\d.]{4,}\.\d{1,2}|至今)\s*$"
        if re.match(date_range_pattern, prev_text_stripped):
            final_output += '\n' + curr_line['text']
            continue

        is_prev_line_full = (canvas_width - prev_line['end_x']) < 30
        ends_with_punctuation = prev_text_stripped.endswith(ENDING_PUNCTUATION)
        is_word_wrap = is_prev_line_full and not ends_with_punctuation and vertical_gap < median_line_height * 1.4

        if is_word_wrap:
            # 自动换行，需要智能判断如何连接
            prev_last_char = prev_line['text'].rstrip()[-1] if prev_line['text'].rstrip() else ''
            curr_first_char = clean_curr_text[0]

            # 健壮性检查
            if not prev_last_char:
                final_output += curr_line['text']
                continue

            # Case 1: 两个都是字母数字，很可能是一个被切断的单词，直接拼接
            if is_alphanumeric(prev_last_char) and is_alphanumeric(curr_first_char):
                final_output += curr_line['text'] # 注意：这里是拼接原始的 curr_line.text，保留其前导空格

            # Case 2: 中英文混合，使用jieba进行智能判断
            else:
                char_pair = prev_last_char + curr_first_char
                # 使用jieba的精确模式进行分词
                # 如果分词结果只有一个元素，说明jieba认为它们应该连在一起
                if len(list(jieba.cut(char_pair, cut_all=False))) == 1:
                    final_output += curr_line['text']
                else:
                    # 如果jieba认为它们是两个独立的词，则用空格连接
                    # 这可以防止像 "的Word" 这样的错误拼接
                    final_output += ' ' + curr_line['text']

        elif vertical_gap > median_line_height * 1.6:
            final_output += '\n\n' + curr_line['text']
        else:
            final_output += '\n' + curr_line['text']

    return final_output


def final_engine_v18_word_based(captured_data: list) -> str:
    """
    终极版V22.1 (Section-Driven with Semantic Joining):
    采用“分区驱动”思想，并在每个分区内部应用完整的语义化行间拼接。
    """
    SECTION_KEYWORDS = ["期望职位", "岗位经验", "工作经历", "项目经验", "教育经历", "资格证书", "专业技能",
     "牛人分析器", "在校经历"]
    if not captured_data: return ""

    # 第1步：预处理，计算有效边界
    events = _preprocess_events_for_layout(captured_data)

    # 第2步：识别分区锚点和内容
    section_anchors = []
    content_events = []
    header_events = []

    temp_anchors = sorted([e for e in events if e.get('text','').strip() in SECTION_KEYWORDS], key=lambda e: e.get('y', 0))
    first_anchor_y = temp_anchors[0].get('y', 0) if temp_anchors else float('inf')

    for e in events:
        event_text = e.get('text', '').strip()
        if event_text in SECTION_KEYWORDS:
            section_anchors.append(e)
        elif e.get('y', 0) < first_anchor_y - 5:
            header_events.append(e)
        else:
            content_events.append(e)

    section_anchors.sort(key=lambda e: e.get('y', 0))

    # 第3步：构建节区列表
    sections = []
    if header_events:
        sections.append({'keyword': 'HEADER', 'y_start': -1, 'content_events': header_events})

    for i, anchor in enumerate(section_anchors):
        y_start = anchor.get('y', 0) - 5
        y_end = section_anchors[i+1].get('y', 0) - 5 if i + 1 < len(section_anchors) else float('inf')
        events_for_this_section = [e for e in content_events if y_start <= e.get('y', 0) < y_end]
        sections.append({'keyword': anchor.get('text', ''), 'y_start': y_start, 'content_events': events_for_this_section})

    # 第4步：对每个节区进行独立的、完整的文本重构
    final_text_parts = []
    for section in sections:
        content_text = ""
        if section['content_events']:
            # a. 按y,x排序，聚合为视觉行
            section['content_events'].sort(key=lambda e: (e.get('y', 0), e.get('effective_x', 0)))
            content_lines_events = []
            if section['content_events']:
                current_line = [section['content_events'][0]]
                for i in range(1, len(section['content_events'])):
                    current_event = section['content_events'][i]
                    last_event_in_line = current_line[-1]

                    if abs(current_event.get('y', 0) - last_event_in_line.get('y', 0)) < 10:
                        # 错误的代码: current_line.append(current_line)
                        # 正确的代码: 将当前事件添加到行中
                        current_line.append(current_event)
                    else:
                        content_lines_events.append(current_line)
                        current_line = [current_event]
                content_lines_events.append(current_line)

            # b. 将每行的事件拼接为带元数据的行对象 (不变)
            processed_lines_for_section = []
            for line_events in content_lines_events:
                processed_lines_for_section.append({
                    'text': _reconstruct_lines_from_events(line_events),
                    'y': line_events[0].get('y', 0),
                    'start_x': line_events[0].get('effective_x', 0),
                    'end_x': line_events[-1].get('effective_r', 0)
                })

            # c. 调用语义化行间拼接逻辑 (不变)
            content_text = _join_lines_semantically(processed_lines_for_section)

        if section['keyword'] == 'HEADER':
            final_text_parts.append(content_text)
        else:
            final_text_parts.append(f"{section['keyword']}\n{content_text}")

    # 第5步：用段落间距（两个换行符）连接所有节区
    return "\n\n".join(final_text_parts)


def _process_last_write_wins_on_blocks(events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    【微观去重器】
    对【单份简历】的事件列表，在“词块”层面应用“最后写入者获胜”，
    以处理页面内部的重绘和覆盖。
    """
    if not events: return []

    # 使用一个字典来存储每个位置的最后一个事件
    # key: (y_grid, x_grid), value: event_dict
    state_map = {}
    grid_size = 2  # 使用一个精细的、固定的grid_size

    def get_key(item):
        # 嵌套一个简单的key生成函数
        if 'y' not in item or 'x' not in item: return None
        return (int(item['y'] // grid_size), int(item['x'] // grid_size))

    for event in events:
        if event and event.get('type') in ['fill', 'path']:
            key = get_key(event.get('points', [{}])[0] if event.get('type') == 'path' else event)
            if key:
                state_map[key] = event

    # 返回去重后的事件列表
    return list(state_map.values())

# ------------------------------------------------------------------------------
# 主流程与业务逻辑封装
# ------------------------------------------------------------------------------

async def _extract_and_rebuild_text(frame: Frame, resume_name: Optional[str]) -> str | None:
    try:
        logger.info("开始使用V8提取器 + V18重构引擎处理Canvas文本...")
        anchor_extractor = StatelessNameAnchorExtractor(anchor_name=resume_name)
        all_events = await frame.evaluate("() => window.canvasEvents")
        await frame.evaluate("() => window.resetCanvasEvents()")

        logger.info(f"Fetched and reset {len(all_events)} raw canvas events.")
        if not all_events:
            return None

        # 【阶段一：宏观去重】
        # 使用锚点切分法，解决【跨简历】的数据污染
        logger.info("Pipeline Step 1: Slicing latest events using name anchor (Cross-resume deduplication)...")
        latest_events_potentially_dirty = anchor_extractor.get_latest_resume_events(all_events)

        # 【阶段二：微观去重】
        # 使用“最后写入者获胜”，解决【简历内】的重绘覆盖
        logger.info("Pipeline Step 2: Applying Last-Write-Wins (Intra-resume deduplication)...")
        clean_events = _process_last_write_wins_on_blocks(latest_events_potentially_dirty)
        if not clean_events:
            logger.warning("Deduplication step resulted in no events. Aborting.")
            return None
        logger.info(f"Deduplication successful. Produced {len(clean_events)} clean event blocks.")

        # 【阶段三：文本重构】
        # 将100%干净的事件送入重构引擎
        logger.info("Pipeline Step 3: Reconstructing text from clean event blocks...")
        final_text = final_engine_v18_word_based(clean_events)

        return final_text
    except Exception as e:
        logger.warning(f"在 _extract_and_rebuild_text 流程中提取文本失败: {e}")
        return None


async def get_geek_info_and_frame(page: Page) -> tuple[Frame | None, str | None]:
    try:
        logger.info("详情页：正在定位最终的简历iframe...")
        resume_frame: Frame = page.frame(name="recommendFrame")
        if not resume_frame:
            await page.wait_for_selector('iframe[name="recommendFrame"]', timeout=5000)
            resume_frame = page.frame(name="recommendFrame")
        if not resume_frame: raise Exception("未能找到外部 recommendFrame")
        await resume_frame.wait_for_selector("div.resume-summary", timeout=15000)
        resume_summary_html_content = ""
        element_handle = await resume_frame.query_selector("div.resume-summary")
        if element_handle:
            resume_summary_html_content = await element_handle.evaluate("e => e.outerHTML")
        inner_iframe_selector = 'iframe[src*="/web/frame/c-resume/"]'
        await resume_frame.wait_for_selector(inner_iframe_selector, timeout=5000)
        target_frame: Frame = None
        for frame in resume_frame.child_frames:
            if "/web/frame/c-resume/" in frame.url:
                target_frame = frame
                break
        if not target_frame: raise Exception("未能找到内部 c-resume frame")
        logger.info("详情页：成功进入简历Canvas所在的iframe。")
        return target_frame, resume_summary_html_content
    except Exception as e:
        logger.error(f"在get_geek_info_and_frame中定位iframe时发生错误: {e}", exc_info=True)
        return None, None


class HybridStructureExtractor:
    SECTION_KEYWORDS = ["期望职位", "岗位经验", "工作经历", "项目经验", "教育经历", "资格证书", "专业技能",
                        "牛人分析器", "在校经历"]
    POSITION_KEYWORDS = ['工程师', '开发', '测试', '经理', '主管', '总监', 'leader', '运维', '架构师', 'Java', '组长',
                         '组员', 'Android', '前端']
    SKILL_BLACKLIST = ['职责', '业绩', '内容', '项目', '平台', '方案', '简介', '描述']
    VERB_BLACKLIST = ['使用', '实现', '负责', '参与', '主导', '基于', '集成', '完成', '编写', '优化', '协助', '负责']

    def __init__(self, final_text: str, api_data: dict, html_content: str):
        self.full_text = final_text
        self.api_data = api_data or {}
        self.html_content = html_content

    def _create_keyword_pattern(self, keyword: str) -> str:
        escaped_chars = [re.escape(char) for char in keyword]
        return r'\s*'.join(escaped_chars)

    def parse(self) -> dict:
        final_result = self._parse_from_api()
        dom_exps = self._parse_from_dom()
        text_exps_details = self._parse_details_from_text()
        final_result['candidate']['workExperiences'] = self._merge_dom_and_text_details(
            dom_exps.get('workExperiences', []), text_exps_details.get('workExperiences', []))
        final_result['candidate']['projectExperiences'] = self._merge_dom_and_text_details(
            dom_exps.get('projectExperiences', []), text_exps_details.get('projectExperiences', []))
        final_result['candidate']['certifications'] = text_exps_details.get('certifications', "")
        if dom_exps.get('educations'):
            for edu in dom_exps.get('educations'):
                edu['isPartTime'] = text_exps_details.get('isPartTime', '')
            final_result['candidate']['educations'] = dom_exps.get('educations')
        sections = self._find_all_sections_text()
        final_result['candidate']['jobExperiences'] = self._parse_job_experiences_from_text(
            sections.get("岗位经验", ""))
        final_result['candidate']['niurenAnalyze'] = self._parse_niuren_analyzer(sections.get("牛人分析器", ""))
        if final_result['candidate'].get('niurenAnalyze'): final_result['candidate']['niurenAnalyze'][
            'financingSize'] = ""

        # a. 从API获取基础的期望职位列表
        api_expectations = final_result['candidate'].get('expectations', [])
        # b. 获取期望职位的文本块
        expectations_text = sections.get("期望职位", "")
        # c. 调用新的增强函数
        # 它会以API数据为基础，并用文本信息去补充它
        enhanced_expectations = self._enhance_expectations_from_text(expectations_text, api_expectations)

        # d. 用增强后的结果更新最终数据
        final_result['candidate']['expectations'] = enhanced_expectations

        return final_result

    def _parse_from_dom(self) -> dict:
        if not self.html_content: return {}
        soup = BeautifulSoup(self.html_content, 'html.parser')
        result = {"workExperiences": [], "projectExperiences": [], "educations": []}
        work_ul = soup.select_one('ul.jobs:not(.project):not(.education)')
        if work_ul:
            for li in work_ul.select('li'):
                company, dates, position, duration = li.select_one('h3 > span'), li.select_one(
                    'h3 > em'), li.select_one('p > span'), li.select_one('p > em')
                start_date, end_date = (dates.get_text(strip=True).split(' - ') + [''])[:2] if dates else ('', '')
                result['workExperiences'].append({"companyName": company.get_text(strip=True) if company else None,
                                                  "positionType": position.get_text(strip=True) if position else None,
                                                  "startDate": start_date, "endDate": end_date or '至今',
                                                  "duration": duration.get_text(strip=True) if duration else None})
        project_ul = soup.select_one('ul.jobs.project')
        if project_ul:
            for li in project_ul.select('li'):
                project, dates, position, duration = li.select_one('h3 > span'), li.select_one(
                    'h3 > em'), li.select_one('p > span'), li.select_one('p > em')
                start_date, end_date = (dates.get_text(strip=True).split(' - ') + [''])[:2] if dates else ('', '')
                result['projectExperiences'].append({"projectName": project.get_text(strip=True) if project else None,
                                                     "positionType": position.get_text(
                                                         strip=True) if position else None, "startDate": start_date,
                                                     "endDate": end_date or '至今',
                                                     "duration": duration.get_text(strip=True) if duration else None})
        edu_ul = soup.select_one('ul.jobs.education')
        if edu_ul:
            for li in edu_ul.select('li'):
                school, dates, details_p = li.select_one('h3 > span'), li.select_one('h3 > em'), li.select_one('p')
                major, edu_type = None, None
                if details_p:
                    parts = [p.strip() for p in details_p.get_text(separator='•').split('•') if p.strip()]
                    if len(parts) >= 2:
                        major, edu_type = parts[0], parts[1]
                    elif len(parts) == 1:
                        edu_type = parts[0]
                start_date, end_date = (dates.get_text(strip=True).split(' - ') + [''])[:2] if dates else ('', '')
                result['educations'].append(
                    {"schoolName": school.get_text(strip=True) if school else None, "majorName": major,
                     "isPartTime": "", "educationType": edu_type, "startDate": start_date, "endDate": end_date,
                     "educationDetails": ""})
        return result

    def _parse_details_from_text(self) -> dict:
        result = {"workExperiences": [], "projectExperiences": [], "isPartTime": "", "certifications": ""}
        sections = self._find_all_sections_text()
        work_text = sections.get("工作经历", "")
        if work_text:
            header_pattern = re.compile(r"^\S.*?([\d.]{4,}\.\d{1,2})\s*-\s*([\d.]{4,}\.\d{1,2}|至今)\s*$", re.MULTILINE)
            matches = list(header_pattern.finditer(work_text))
            for i, match in enumerate(matches):
                start_pos, end_pos = match.end(), matches[i + 1].start() if i + 1 < len(matches) else len(work_text)
                details, skills = self._post_process_details(work_text[start_pos:end_pos].strip())
                result['workExperiences'].append(
                    {"match_key": match.group(0).strip(), "jobDetails": details, "tags": ",".join(skills)})
        project_text = sections.get("项目经验", "")
        if project_text:
            header_pattern = re.compile(r"^\S.*?([\d.]{4,}\.\d{1,2})\s*-\s*([\d.]{4,}\.\d{1,2}|至今)\s*$", re.MULTILINE)
            matches = list(header_pattern.finditer(project_text))
            for i, match in enumerate(matches):
                start_pos, end_pos = match.end(), matches[i + 1].start() if i + 1 < len(matches) else len(project_text)
                details, _ = self._post_process_details(project_text[start_pos:end_pos].strip())
                result['projectExperiences'].append({"match_key": match.group(0).strip(), "projectDetails": details})
        if sections.get("教育经历", "") and '非全日制' in sections.get("教育经历", ""): result['isPartTime'] = "1"
        result['certifications'] = re.sub(r"\s+", ",", sections.get("资格证书", ""))
        return result

    # 期望职位解析逻辑
    def _enhance_expectations_from_text(self, text: str, api_expectations: List[Dict]) -> List[Dict]:
        """
        V36.2核心辅助函数：先对所有文本进行去空格规范化，
        然后利用API信息，逐步剥离已知部分，以提取 jobIndustry。
        """
        if not text or not api_expectations:
            return api_expectations

        base_expectation = api_expectations[0]

        # API优先原则
        if base_expectation.get("jobIndustry") and base_expectation.get("jobIndustry") != "行业不限":
            return api_expectations

        # --- “规范化-替换”式精确提取 ---

        # 1. 对原始文本进行一次性去空格规范化
        # 使用正则表达式 \s+ 匹配一个或多个空白字符（包括空格、制表符、换行符）
        normalized_text = re.sub(r'\s+', '', text.strip())

        # 2. 从薪资中提取，并从规范化文本中移除
        salary_match = re.search(r'(\d+-\d+K|面议)$', normalized_text)
        if salary_match:
            salary_from_text = salary_match.group(1)
            # 从规范化文本的末尾移除薪资
            normalized_text = normalized_text[:salary_match.start()]
            if not base_expectation.get("jobPayrollMonths"):
                base_expectation["jobPayrollMonths"] = salary_from_text

        # 3. 从API字典中提取城市信息，规范化后移除
        city = base_expectation.get("city")
        if city:
            # 城市名也可能包含空格或'&'，都需要规范化
            normalized_city = re.sub(r'\s*&\s*', '', city).strip()
            # 从规范化文本的开头移除城市
            if normalized_text.startswith(normalized_city):
                normalized_text = normalized_text[len(normalized_city):]

        # 4. 从API字典中提取职位类型信息，规范化后移除
        job_type = base_expectation.get("jobType")
        if job_type:
            normalized_job_type = re.sub(r'\s+', '', job_type).strip()
            # 职位类型可能出现在文本的任何位置，直接替换
            normalized_text = normalized_text.replace(normalized_job_type, '')

        # 5. 移除所有可能的分隔符
        normalized_text = normalized_text.replace('|', '')

        # 6. 经过层层剥离，剩下的就是最纯粹的行业信息
        final_industry = normalized_text.strip()
        if final_industry:
            base_expectation["jobIndustry"] = final_industry
        elif not base_expectation.get("jobIndustry"):
            # 如果什么都没剩下，且API中也没有，则设为None
            base_expectation["jobIndustry"] = None

        return [base_expectation]

    def _merge_dom_and_text_details(self, dom_exps, text_details_list):
        """
        V35.2 增强：在进行匹配时，统一转换为小写并移除空格，
        实现大小写不敏感的、格式不敏感的终极匹配。
        """
        for dom_exp in dom_exps:
            name_key = "companyName" if "companyName" in dom_exp else "projectName"
            dom_match_key_part1 = dom_exp.get(name_key)
            dom_match_key_part2 = dom_exp.get('positionType')

            best_match = None

            # 引入大小写不敏感的规范化

            # 1. 创建DOM匹配键的规范化版本 (去空格 + 转小写)
            def normalize_key(key_str):
                if not key_str: return None
                return re.sub(r'\s+', '', key_str).lower()

            dom_key1_normalized = normalize_key(dom_match_key_part1)
            dom_key2_normalized = normalize_key(dom_match_key_part2)
            # modifications

            for text_detail in text_details_list:
                text_key = text_detail['match_key']

                # 2. 创建文本标题的规范化版本
                text_key_normalized = normalize_key(text_key)

                # 3. 使用规范化后的变量进行 'in' 判断
                # 增加健壮性检查，确保 key_normalized 不为 None
                is_part1_match = dom_key1_normalized and text_key_normalized and dom_key1_normalized in text_key_normalized
                is_part2_match = not dom_key2_normalized or (text_key_normalized and dom_key2_normalized in text_key_normalized)

                if is_part1_match and is_part2_match:
                    best_match = text_detail
                    break

            if best_match:
                # 匹配成功后，依然使用原始的 best_match 对象进行更新
                dom_exp.update(best_match)
                if 'match_key' in dom_exp: del dom_exp['match_key']
                text_details_list.remove(best_match)

        return dom_exps

    def _parse_from_api(self) -> dict:
        geek_card = self.api_data.get("geekCard", {})
        candidate_info = {"candicateId": geek_card.get('encryptGeekId'),
                          "activeStatus": self.api_data.get('activeTimeDesc') or "刚刚活跃", "educations": [],
                          "candicateName": geek_card.get('geekName'), "candicateAge": geek_card.get('ageDesc'),
                          "avatar": str(geek_card.get('geekAvatar', '')),
                          "candicateWorkExperience": geek_card.get('geekWorkYear'), "candicatePhone": "",
                          "candicateEmail": "", "candicateStatus": geek_card.get('applyStatusDesc'),
                          "candicateIntroduction": geek_card.get('geekDesc', {}).get('content'),
                          "candicateSex": str(geek_card.get('geekGender', '')), "canSayHello": "1",
                          "candicateHometown": "", "candicateCurrentCity": "", "candicateResumeUrl": "",
                          "filterType": "1", "workExperiences": [], "expectations": [], "certifications": ""}
        expect_data = geek_card.get('viewExpect', {})
        expectLocationName = geek_card.get('expectLocationName', "")
        expectPositionName = geek_card.get('expectPositionName', "")
        salary = geek_card.get('salary') or f"{expect_data.get('lowSalary', 'N')}-{expect_data.get('highSalary', 'A')}K"
        if expect_data.get('locationName'):
            locationName = expect_data.get('locationName')
            if expectLocationName and locationName and expectLocationName != locationName:
                expectLocationName += ' & ' + locationName
            elif not expectLocationName and locationName:
                expectLocationName = locationName
            candidate_info['expectations'].append(
                {"city": expectLocationName, "jobType": expectPositionName, "jobIndustry": "行业不限",
                 "jobPayrollMonths": salary})
        candidate_info['educations'] = [
            {"schoolName": edu.get('school'), "majorName": edu.get('major'), "educationType": edu.get('degreeName'),
             "startDate": edu.get('startDate'), "isPartTime": "", "endDate": edu.get('endDate')} for edu in
            geek_card.get('geekEdus', [])]
        return {"candidate": candidate_info}

    def _find_all_sections_text(self) -> dict:
        cleaned_text = re.sub(r'^[!！]', '', self.full_text, flags=re.MULTILINE)
        sections, text = {}, cleaned_text
        indices = {}
        for kw in self.SECTION_KEYWORDS:
            pattern = self._create_keyword_pattern(kw)
            match = re.search(r'(?:^|\n)\s*(' + pattern + r')', text, re.I)
            if match: indices[kw] = match.start(1)
        if not indices: return {}
        sorted_keywords = sorted(indices.keys(), key=indices.get)
        for i, keyword in enumerate(sorted_keywords):
            start_pos, end_pos = indices[keyword], indices[sorted_keywords[i + 1]] if i + 1 < len(
                sorted_keywords) else len(text)
            content = re.sub(r'^\s*' + self._create_keyword_pattern(keyword) + r'\s*', '',
                             text[start_pos:end_pos].strip(), count=1, flags=re.I).strip()
            sections[keyword] = content
        return sections

    def _parse_expectations_from_text(self, text: str) -> list:
        if not text: return []
        text, salary = text.strip(), None
        salary_match = re.search(r'((\d+-\d+K)|面议)$', text)
        if salary_match:
            salary = salary_match.group(1)
            text = text[:salary_match.start()].strip()
        parts = text.split()
        if not parts: return []
        location = parts.pop(0)
        job_type_parts, industry_parts, parsing_job_type = [], [], True
        for part in parts:
            if parsing_job_type and any(kw.lower() in part.lower() for kw in self.POSITION_KEYWORDS):
                job_type_parts.append(part)
            else:
                parsing_job_type = False
                industry_parts.append(part)
        return [{"city": location, "jobType": " ".join(job_type_parts) if job_type_parts else None,
                 "jobIndustry": " ".join(industry_parts) if industry_parts else "行业不限", "jobPayrollMonths": salary}]

    def _parse_job_experiences_from_text(self, text: str) -> list:
        if not text: return []
        return [{"jobType": match[0], "workExperience": match[1]} for match in
                re.findall(r'(\S+)\s+(\d+年(?:\d+个月)?)', text)]

    def _parse_niuren_analyzer(self, text: str) -> dict:
        if not text: return {}
        patterns = {"intention": r'求职意愿\s*([^，。]+)', "popularity": r'受欢迎程度\s*([^，。]+)',
                    "chatDuration": r'通常活跃时间为\s*([^。]+)', "companySize": r'更喜欢\s*([\d,-]+人)',
                    "workArea": r'偏好工作地点\s*([^。]+)'}
        return {key: match.group(1).strip() for key, pattern in patterns.items() if (match := re.search(pattern, text))}

    def _post_process_details(self, details_raw: str) -> (str, list):
        skills_set = set()
        tech_patterns = [r'^\s*(?:项目技术|开发技术|项目框架|开发环境)\s*:\s*(.*)']
        remaining_lines, lines = [], details_raw.strip().split('\n')
        for line in lines:
            extracted = False
            for pattern in tech_patterns:
                if match := re.match(pattern, line, re.IGNORECASE):
                    skills_str = match.group(1)
                    cleaned_skills_str = re.sub(r'[、，；+,]', ' ', skills_str)
                    skills_set.update(s.strip() for s in re.split(r'\s+', cleaned_skills_str) if
                                      len(s.strip()) > 1 and s.strip().lower() != '等')
                    extracted = True
                    break
            if not extracted: remaining_lines.append(line)
        details_text_cleaned, lines_for_generic_search = "\n".join(remaining_lines), "\n".join(
            remaining_lines).strip().split('\n')
        details_lines_final, potential_skill_lines_buffer = [], []
        for line in reversed(lines_for_generic_search):
            line = line.strip()
            if not line: continue
            if re.match(r'^[●*-]', line) or re.match(r'^\d+[.,、]\s*', line) or any(
                    bl_word in line for bl_word in self.SKILL_BLACKLIST):
                details_lines_final.insert(0, line);
                continue
            if (not any(p in line for p in '，。；：()（）') and re.search(r'\s', line) and len(line) < 150 and not any(
                    line.startswith(v) for v in self.VERB_BLACKLIST)):
                potential_skill_lines_buffer.insert(0, line)
            else:
                details_lines_final.insert(0, line)
                if potential_skill_lines_buffer: break
        if (num_remaining_lines := len(lines_for_generic_search) - len(potential_skill_lines_buffer) - len(
                details_lines_final)) > 0:
            details_lines_final = lines_for_generic_search[:num_remaining_lines] + details_lines_final
        for skill_line in potential_skill_lines_buffer: skills_set.update(s for s in re.split(r'\s+', skill_line) if s)
        return "\n".join(details_lines_final).strip(), sorted(list(filter(None, skills_set)), key=str.lower)


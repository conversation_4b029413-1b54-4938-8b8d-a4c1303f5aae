# -*- coding: utf-8 -*-
"""
任务派发工具模块
提供统一的、可复用的函数来向 Worker 派发任务。
"""
import json
import time
import uuid
from typing import Any, Dict

import redis
from fastapi import HTTPException

from src.conf.config import CONFIG
from src.routers.api_result import get_result_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 初始化同步 Redis 客户端
redis_client = redis.from_url(CONFIG.Redis.URL, decode_responses=True)

async def dispatch_async(action: str, payload: Dict[str, Any]) -> str:
    """ 
    异步派发一个任务（发后不理）。
    
    Args:
        action: 任务动作。
        payload: 任务负载。

    Returns:
        str: 生成的任务ID。
    """
    # 生成唯一的任务ID
    task_id = str(uuid.uuid4())

    if payload.get("batchNo"):
        task_id = payload.get("batchNo")
    task_payload = {
        "id": task_id,
        "action": action,
        "payload": payload,
        "timestamp": time.time()
    }
    redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}", json.dumps(task_payload))
    logger.info(f"已异步派发任务: {action}, ID: {task_id}")
    return task_id

async def dispatch_sync(
    action: str, 
    payload: Dict[str, Any], 
    timeout: int = 120
) -> Dict[str, Any]:
    """
    分发一个任务并同步等待其结果。

    Args:
        action: 任务动作。
        payload: 任务负载。
        timeout: 等待结果的超时时间（秒）。

    Returns:
        Dict: Worker 返回的任务结果。
    """
    task_id = str(uuid.uuid4())
    result_manager = get_result_manager()
    
    task_payload = {
        "id": task_id,
        "action": action,
        "result_channel": f"{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.worker_ip}",
        "payload": payload,
        "timestamp": time.time()
    }
    
    redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}", json.dumps(task_payload))
    logger.info(f"已同步派发任务: {action}, ID: {task_id}, 等待结果... (超时: {timeout}s)")
    
    result = await result_manager.wait_for_result(task_id, timeout=timeout)
    return result

async def dispatch_async_locked(
    action: str, 
    payload: Dict[str, Any],
    lock_key: str,
    lock_timeout: int = CONFIG.Redis.TASK_LOCK_TIMEOUT_SECONDS
) -> str:
    """
    使用分布式锁异步分发任务，防止重复执行。

    Args:
        action: 任务动作。
        payload: 任务负载。
        lock_key: 用于分布式锁的 Redis 键。
        lock_timeout: 锁的超时时间（秒）。

    Returns:
        str: 生成的任务ID。

    Raises:
        HTTPException: 如果无法获取锁（任务已在运行）。
    """
    if not redis_client.set(lock_key, "locked", ex=lock_timeout, nx=True):
        logger.warning(f"获取任务锁失败，任务可能已在运行。Lock Key: {lock_key}")
        raise HTTPException(status_code=409, detail=f"Task with action '{action}' is already running or queued.")

    task_id = str(uuid.uuid4())
    task_payload = {
        "id": task_id, 
        "action": action,
        "lock_key": lock_key, 
        "payload": payload,
        "timestamp": time.time()
    }
    
    redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}", json.dumps(task_payload))
    logger.info(f"已派发带锁任务: {action}, ID: {task_id}, Lock Key: {lock_key}")
    return task_id

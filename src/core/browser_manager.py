import asyncio
import json
import os
import random
import re
import time

from aiolimiter import AsyncLimiter
from playwright.async_api import Page, Frame
from playwright.async_api import async_playwright

from src.conf.config import CONFIG
from src.utils.logger import get_logger

# 动态速率限制：随机化请求间隔，更像人类行为
# 基础速率：每 3-8 秒最多 6-16 个请求
base_rate = random.randint(6, 16)
base_period = random.uniform(3, 8)
limiter = AsyncLimiter(base_rate, base_period)

logger = get_logger(__name__)

async def handle_route(route, request):
    """
    增强的请求路由处理，添加随机延迟和更自然的请求处理
    """
    if CONFIG.BossZhiPin.API['chat_url'] in request.url:
        # 添加随机延迟，模拟网络波动
        delay = random.uniform(0.1, 0.5)
        await asyncio.sleep(delay)

        logger.info(f"  -> 请求 {request.url} 到达，申请令牌... (时间: {time.time():.2f})")

        async with limiter:
            # 再次添加微小随机延迟
            await asyncio.sleep(random.uniform(0.05, 0.2))
            logger.info(f"✅ 令牌获取成功！放行请求 {request.url} (时间: {time.time():.2f})")
            await route.continue_()
    else:
        # 对其他请求也添加轻微延迟，避免请求过于密集
        if random.random() < 0.3:  # 30%概率添加延迟
            await asyncio.sleep(random.uniform(0.01, 0.1))
        await route.continue_()

# 简化的反检测hook脚本（针对固定用户场景）
js_hook_script = """
(function() {
    // 1. 确保脚本只注入一次
    if (window.canvasStyleHooked) {
        return;
    }
    window.canvasStyleHooked = true;

    // 2. 使用WeakMap来存储每个canvas context实例的独立状态
    const contextState = new WeakMap();

    // 3. 定义我们想要追踪的样式属性列表
    const propertiesToHook = [
        'font', 'fillStyle', 'strokeStyle', 'textAlign', 
        'textBaseline', 'direction', 'globalAlpha'
    ];

    // 4. 通用的属性Hook辅助函数
    const hookProperty = (propName) => {
        const descriptor = Object.getOwnPropertyDescriptor(CanvasRenderingContext2D.prototype, propName);
        // 健壮性检查：确保属性存在且可配置
        if (!descriptor || !descriptor.set) return;

        const originalSetter = descriptor.set;

        Object.defineProperty(CanvasRenderingContext2D.prototype, propName, {
            configurable: true,
            set: function(value) {
                if (!contextState.has(this)) {
                    contextState.set(this, {});
                }
                const state = contextState.get(this);
                state[propName] = value;
                return originalSetter.apply(this, arguments);
            }
        });
    };

    // 5. 遍历并Hook所有我们关心的属性
    propertiesToHook.forEach(hookProperty);

    // 6. 初始化事件数组和重置函数
    window.canvasEvents = [];
    window.resetCanvasEvents = () => {
        window.canvasEvents = [];
    };

    // 7. 增强版的 fillText Hook
    const originalFillText = CanvasRenderingContext2D.prototype.fillText;
    CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {
        // 捕获所有非空文本
        // if (text && text.toString().length > 0) {
        const currentStyle = contextState.get(this) || {};
        // 在捕获事件时，同时测量并记录文本宽度
        const measuredWidth = this.measureText(text).width;
        // 将完整的事件信息（包括样式）推入数组
        window.canvasEvents.push({ 
            type: 'fill', 
            text: text.toString(), // 确保是字符串
            x: x, 
            y: y,
            width: measuredWidth,
            style: { ...currentStyle } // 核心：附带了完整的样式信息
        });
        //}
        return originalFillText.apply(this, arguments);
    };

    console.log("Ultimate canvas hook (V9, with style capturing) initialized.");

    // 基础反检测：隐藏自动化特征
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
    });

    // 保持固定的插件数量（模拟真实用户环境）
    Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
    });

    // 固定的语言设置（符合中国用户习惯）
    Object.defineProperty(navigator, 'languages', {
        get: () => ['zh-CN', 'zh', 'en-US', 'en'],
    });

    // 添加固定的用户交互痕迹
    window.addEventListener('load', function() {
        // 模拟用户已经进行过一些交互
        window.performance.mark('user-interaction-start');
        setTimeout(() => {
            window.performance.mark('user-interaction-end');
        }, 800);  // 固定延迟，模拟正常用户反应时间
    });
})();
"""

async def init_driver(auto_load_cookies: bool = True) -> Page:
    """
    增强的浏览器初始化，提升反检测能力和稳定性

    :param auto_load_cookies: 是否自动加载cookie文件，默认为True
    :return: Playwright 浏览器唯一Page实例
    """
    browser_type = CONFIG.Crawler.PLAYWRIGHT['browser_type']

    try:
        logger.info("正在初始化增强反检测 Playwright 浏览器...")
        playwright = await async_playwright().start()

        # 增强的启动参数，提升反检测能力
        launch_options = {
            "headless": CONFIG.Crawler.PLAYWRIGHT['headless'],
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-automation",
                "--enable-automation=false",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-extensions-except",
                '--log-level=3',
            ],
            "ignore_default_args": [
                "--enable-automation",
                "--enable-blink-features=AutomationControlled"
            ],
            "slow_mo": 80 # 固定适中的操作延迟，模拟正常用户操作速度
        }

        if browser_type == "chrome":
            browser = await playwright.chromium.launch(channel="chrome", **launch_options)

            # 注入hook 脚本
            context = await browser.new_context()
            # 最佳实践：在 Context 级别注入脚本
            # 这样，这个 context 打开的所有页面（包括 iframe）都会自动应用这个脚本
            await context.add_init_script(js_hook_script)

            # 自动加载cookie（如果启用且存在cookie文件）
            if auto_load_cookies:
                await auto_load_user_cookies(context)

            # 复用第一个标签页，没有则新建
            page = context.pages[0] if context.pages else await context.new_page()

            # 拦截所有目标 URL 的请求
            await page.route("**/*", handle_route)

            # 获取 Headers
            await get_default_headers(page)

            # 初始化tracing管理器（但不立即启动tracing）
            from src.utils.tracing_manager import get_tracing_manager
            get_tracing_manager(page)
            logger.info("已初始化tracing管理器")

            return page
        else:
            raise ValueError(f"不支持的浏览器类型: {browser_type}")
    except Exception as e:
        logger.error(f"浏览器初始化失败: {str(e)}")
        raise ValueError(f"浏览器初始化失败: {str(e)}")

async def get_default_headers(page: Page):
    headers = load_headers_from_file(None)

    if headers is None:
        logger.info(f"\n--- 正在访问 {CONFIG.Header.HEADERS_TEST_URL} 以获取请求头 ---")
        await page.goto(CONFIG.Header.HEADERS_TEST_URL)
        headers_json_str = await page.inner_text('pre')
        logger.info("观察到的 Headers 如下:")
        logger.info(headers_json_str)

        try:
            # 1. 解析从页面获取的 JSON 字符串
            headers_data = json.loads(headers_json_str)
            refined_headers = headers_data['headers'].copy()

            keys_to_remove = ['host', 'content-length', 'content-type', 'x-amzn-trace-id']
            # 遍历原始键的列表进行删除，以防在迭代时修改字典
            for key in list(refined_headers.keys()):
                if key.lower() in keys_to_remove:
                    del refined_headers[key]
                    logger.info(f"  - (清洗): 移除了键 '{key}'")
            # 定义要添加或覆盖的键
            refined_headers['Referer'] = CONFIG.BossZhiPin.API['login_url']
            logger.info(f"  + (增强): 添加/更新了 Referer 为 '{refined_headers['Referer']}'")

            # 2. 以格式化的形式（带缩进）写入文件
            with open(CONFIG.Header.HEADERS_OUTPUT_FILE, 'w', encoding='utf-8') as f:
                json.dump(refined_headers, f, ensure_ascii=False, indent=4)

            logger.info(f"✅ Headers 已成功保存到 {os.path.abspath(CONFIG.Header.HEADERS_OUTPUT_FILE)}")

        except (json.JSONDecodeError, IOError) as e:
            logger.info(f"❌ 保存 Headers 到文件时出错: {e}")
            raise ValueError(f"❌ 保存 Headers 到文件时出错: {e}")

def load_headers_from_file(referer: str | None) -> dict | None:
    """
    从指定的 JSON 文件中安全地加载请求头。
    :return: 一个包含请求头的字典，如果文件不存在、格式错误或结构不正确，则返回 None。
    """
    logger.info(f"--- 正在尝试从文件 '{CONFIG.Header.HEADERS_OUTPUT_FILE}' 加载 Headers ---")

    if not os.path.exists(CONFIG.Header.HEADERS_OUTPUT_FILE):
        logger.info(f"❌ 错误: 文件未找到 at '{os.path.abspath(CONFIG.Header.HEADERS_OUTPUT_FILE)}'")
        return None

    try:
        with open(CONFIG.Header.HEADERS_OUTPUT_FILE, 'r', encoding='utf-8') as f:
            headers = json.load(f)

        if referer:
            headers['Referer'] = referer

        # 验证数据结构
        if not isinstance(headers, dict):
            logger.info(f"❌ 错误: JSON 文件的顶层不是一个字典 (dictionary)。")
            return None

        logger.info("✅ Headers 加载成功。")
        return headers

    except json.JSONDecodeError:
        logger.info(f"❌ 错误: 文件 '{CONFIG.Header.HEADERS_OUTPUT_FILE}' 不是一个有效的 JSON 文件。")
        return None
    except Exception as e:
        logger.info(f"❌ 加载文件时发生未知错误: {e}")
        return None

def load_cookies_from_file(account_name, return_type = 1):
    login_file = CONFIG.FilePaths.LOGIN_DATA.format(account_name=account_name)
    cookies = {}
    if os.path.exists(login_file):
        with open(login_file, "r", encoding="utf-8") as f:
            login_data = json.load(f)
        if login_data.get("cookies"):
            cookies_json = login_data["cookies"]
            if return_type == 2:
                return cookies_json
            else:
                for cookie in cookies_json:
                    cookies.update({cookie["name"]: cookie["value"]})
                return cookies
        return None
    else:
        logger.error("cookie文件不存在，未登陆")
        return None


async def auto_load_user_cookies(context):
    """
    自动检测并加载用户cookie文件

    :param context: Playwright浏览器上下文
    """
    import glob

    # 查找所有匹配的用户cookie文件
    data_dir = "./data"
    pattern = os.path.join(data_dir, "user_*.json")
    cookie_files = glob.glob(pattern)

    if not cookie_files:
        logger.info("未找到用户cookie文件，跳过自动加载")
        return

    # 如果有多个文件，选择最新的一个
    if len(cookie_files) > 1:
        # 按修改时间排序，选择最新的
        cookie_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        logger.info(f"找到多个cookie文件，选择最新的: {cookie_files[0]}")

    cookie_file = cookie_files[0]

    try:
        # 从文件名提取账号名
        filename = os.path.basename(cookie_file)
        account_name = filename.replace("user_", "").replace(".json", "")

        logger.info(f"正在自动加载用户 '{account_name}' 的cookie文件: {cookie_file}")

        # 加载cookie数据
        with open(cookie_file, "r", encoding="utf-8") as f:
            login_data = json.load(f)

        if login_data.get("cookies"):
            cookies = login_data["cookies"]

            # 添加cookie到浏览器上下文
            await context.add_cookies(cookies)

            logger.info(f"✅ 成功自动加载 {len(cookies)} 个cookie到浏览器")
            logger.info(f"账号: {account_name}")

            # 设置环境变量，供其他模块使用
            os.environ['account_name'] = account_name
            logger.info(f"已设置环境变量 account_name = {account_name}")

        else:
            logger.warning(f"cookie文件 {cookie_file} 中没有找到cookies数据")

    except Exception as e:
        logger.error(f"自动加载cookie失败: {e}")
        logger.info("将继续使用无cookie状态启动浏览器")


def find_available_user_cookies():
    """
    查找可用的用户cookie文件

    :return: 可用的cookie文件列表，包含文件路径和账号名
    """
    import glob

    data_dir = "./data"
    pattern = os.path.join(data_dir, "user_*.json")
    cookie_files = glob.glob(pattern)

    available_cookies = []

    for cookie_file in cookie_files:
        try:
            # 从文件名提取账号名
            filename = os.path.basename(cookie_file)
            account_name = filename.replace("user_", "").replace(".json", "")

            # 检查文件是否有效
            with open(cookie_file, "r", encoding="utf-8") as f:
                login_data = json.load(f)

            if login_data.get("cookies"):
                file_mtime = os.path.getmtime(cookie_file)
                available_cookies.append({
                    "file_path": cookie_file,
                    "account_name": account_name,
                    "modified_time": file_mtime,
                    "cookie_count": len(login_data["cookies"])
                })
        except Exception as e:
            logger.warning(f"跳过无效的cookie文件 {cookie_file}: {e}")

    # 按修改时间排序
    available_cookies.sort(key=lambda x: x["modified_time"], reverse=True)

    return available_cookies


async def simulate_human_mouse_movement(page: Page, start_x: float, start_y: float, end_x: float, end_y: float):
    """
    模拟人类鼠标移动轨迹，使用贝塞尔曲线生成自然的移动路径
    """
    steps = random.randint(10, 20)

    # 生成控制点来创建贝塞尔曲线
    control_x1 = start_x + (end_x - start_x) * 0.25 + random.uniform(-50, 50)
    control_y1 = start_y + (end_y - start_y) * 0.25 + random.uniform(-50, 50)
    control_x2 = start_x + (end_x - start_x) * 0.75 + random.uniform(-50, 50)
    control_y2 = start_y + (end_y - start_y) * 0.75 + random.uniform(-50, 50)

    for i in range(steps + 1):
        t = i / steps
        # 三次贝塞尔曲线公式
        x = (1-t)**3 * start_x + 3*(1-t)**2*t * control_x1 + 3*(1-t)*t**2 * control_x2 + t**3 * end_x
        y = (1-t)**3 * start_y + 3*(1-t)**2*t * control_y1 + 3*(1-t)*t**2 * control_y2 + t**3 * end_y

        await page.mouse.move(x, y)
        await asyncio.sleep(random.uniform(0.01, 0.03))

async def simulate_human_scroll_div(page: Page, frame: Frame, current_scroll: int = 0):
    """
    增强的人类滚动模拟，包含更自然的鼠标移动和停顿行为

    :param current_scroll:
    :param page: Playwright 的 Page 对象，用于调用 mouse.wheel。
    :param frame: 要滚动的目标 Frame 对象。
    """
    # 使用配置文件中的滚动参数
    scroll_step_range = (400, 650)  # 相对固定滚动步长
    delay_range = (CONFIG.Crawler.DELAYS['scroll_delay_min'], CONFIG.Crawler.DELAYS['scroll_delay_max'])

    try:
        scroll_height = await frame.evaluate("() => document.body.scrollHeight")
        logger.info(f"开始增强人类滚动模拟，目标总高度: {scroll_height}px")

        # 定位 frame 的 body 作为悬停和滚动的目标区域
        target_element = frame.locator('div#recommend-list')

        # 获取目标元素的尺寸以便生成随机坐标
        bounding_box = await target_element.bounding_box()
        if not bounding_box:
            logger.error("无法获取 div#recommend-list 的边界框，将使用中心点悬停。")
        else:
            logger.info(f"目标区域尺寸: width={bounding_box['width']}, height={bounding_box['height']}")

        # last_mouse_x, last_mouse_y = 0, 0

        while current_scroll < scroll_height:
            # # 固定用户的阅读停顿
            await asyncio.sleep(random.uniform(1.1, 2.3))

            # 生成随机地滚动步长，有时大有时小
            if random.random() < 0.2:  # 20%概率进行小步滚动
                step = random.randint(100, 300)
            else:
                step = random.randint(*scroll_step_range)

            current_scroll = current_scroll + step

            # 模拟更自然的鼠标移动
            # if bounding_box:
            #     new_x = random.uniform(bounding_box['x'] + 50, bounding_box['x'] + bounding_box['width'] - 50)
            #     new_y = random.uniform(bounding_box['y'] + 50, bounding_box['y'] + bounding_box['height'] - 50)
            #
            #     # 使用贝塞尔曲线移动鼠标
            #     if last_mouse_x != 0 and last_mouse_y != 0:
            #         await simulate_human_mouse_movement(page, last_mouse_x, last_mouse_y, new_x, new_y)
            #     else:
            #         await page.mouse.move(new_x, new_y)
            #
            #     last_mouse_x, last_mouse_y = new_x, new_y
            #
            #     # 随机决定是否点击（模拟用户偶尔的误触）
            #     # if random.random() < 0.05:  # 5%概率
            #     #     await page.mouse.click(new_x, new_y)
            #     #     await asyncio.sleep(random.uniform(0.1, 0.3))
            # else:
            #     await target_element.hover()

            # 模拟更自然地滚动：有时连续滚动，有时分段滚动
            if random.random() < 0.3:  # 30%概率分段滚动
                mini_steps = random.randint(2, 4)
                mini_step = step // mini_steps
                for _ in range(mini_steps):
                    await target_element.hover()
                    await page.mouse.wheel(0, mini_step)
                    await asyncio.sleep(random.uniform(0.1, 0.3))
            else:
                await target_element.hover()
                await page.mouse.wheel(0, step)

            logger.info(f"滚动了 {step}px，当前位置: {current_scroll}/{scroll_height}")

            # 随机化延迟时间
            delay = random.uniform(*delay_range)
            if random.random() < 0.1:  # 10%概率进行额外长的延迟
                delay *= random.uniform(2, 4)
            await asyncio.sleep(delay)

        logger.info("增强人类滚动模拟完成。")
        return current_scroll
    except Exception as e:
        logger.error(f"模拟滚动时发生错误: {e}")
        raise ValueError(f"模拟滚动时发生错误: {e}")


async def get_scroll_info_from_locators(frame: Frame) -> tuple[int, int]:
    """
    【V2.0 优化版】: 完全使用 Playwright Locator API 获取滚动信息。

    :param frame: 目标 Frame 对象。
    :return: 一个元组 (总滚动高度, 当前滚动位置)。
    """
    total_height = 0
    current_scroll = 0

    try:
        # 1. 通过 Frame Locator 定位 div 并获取 style 属性
        height_div_locator = frame.locator('div#resume')
        style_str = await height_div_locator.get_attribute('style')
        if style_str:
            # 在 Python 端用正则解析高度
            match = re.search(r'height:\s*(\d+)px', style_str)
            if match:
                total_height = int(match.group(1))

        # 2. 通过 Frame Locator 定位 canvas 并获取 transform 属性
        canvas_locator = frame.locator('canvas#resume')
        transform_str = await canvas_locator.get_attribute('style')
        if transform_str:
            # 在 Python 端用正则解析 transformY
            match = re.search(r'translateY\(([-.\d]+)px\)', transform_str)
            if match:
                current_scroll = abs(float(match.group(1)))

    except Exception as e:
        logger.error(f"通过Locator获取滚动信息时出错: {e}")

    return total_height, int(current_scroll)

async def simulate_canvas_scroll(page: Page, frame: Frame):
    """
    通过在Canvas的“安全区域”内随机悬停并模拟鼠标滚轮，以高度拟人化的方式滚动指定的Frame。
    这是处理Canvas滚动的最佳实践。

    智能滚动Canvas：首先从指定的div获取总高度，然后监控Canvas的transform属性来精确控制滚动。

    :param page: Playwright 的 Page 对象。
    :param frame: 包含Canvas的目标 Frame 对象。
    """

    # 使用配置文件中的Canvas滚动参数
    scroll_step_range = (550, 600)  # 相对固定地滚动步长
    delay_range = (CONFIG.Crawler.DELAYS['canvas_scroll_delay_min'], CONFIG.Crawler.DELAYS['canvas_scroll_delay_max'])
    reading_pause_probability = CONFIG.Crawler.HUMAN_BEHAVIOR['scroll_pause_probability']
    # backtrack_probability = 0.08  # 8%概率回滚重读（降低频率，更符合熟练用户）

    try:
        # 使用优化后的函数获取初始滚动信息
        total_height, current_scroll = await get_scroll_info_from_locators(frame)
        if total_height == 0:
            raise Exception("无法从 <div id='resume'> 获取到有效的滚动总高度。")
        logger.info(f"成功获取滚动目标，总高度: {total_height}px，当前位置: {current_scroll}px")

        # 高度修正 -700
        total_height = total_height - 700

        canvas_locator = frame.locator('canvas#resume')  # 在循环外定义一次即可

        last_mouse_x, last_mouse_y = 0, 0
        scroll_history = []  # 记录滚动历史，用于回滚

        while current_scroll < total_height:
            # 固定用户的阅读停顿（更短更自然）
            if random.random() < reading_pause_probability:
                pause_time = random.uniform(1.0, 2.0)  # 缩短停顿时间
                logger.info(f"模拟阅读停顿 {pause_time:.1f}s")
                await asyncio.sleep(pause_time)

            ## 随机决定是否回滚重读之前的内容
            # if random.random() < backtrack_probability and scroll_history:
            #     backtrack_steps = random.randint(1, min(3, len(scroll_history)))
            #     logger.info(f"模拟回滚重读，回退 {backtrack_steps} 步")
            #
            #     for _ in range(backtrack_steps):
            #         if scroll_history:
            #             prev_step = scroll_history.pop()
            #             await page.mouse.wheel(0, -prev_step)
            #             await asyncio.sleep(random.uniform(1, 2))
            #
            #     # 重新获取当前位置
            #     _, current_scroll = await get_scroll_info_from_locators(frame)
            #     continue

            # 动态调整延迟时间
            base_delay = random.uniform(*delay_range)
            if random.random() < 0.1:  # 10%概率进行额外长的停顿
                base_delay *= random.uniform(1, 2)

            logger.info(f"滚动等待 {base_delay:.1f}s ......")
            await asyncio.sleep(base_delay)

            # try:
            #     # 增强的鼠标悬停逻辑
            #     bounding_box = await canvas_locator.bounding_box()
            #     if bounding_box:
            #         # 生成更自然的悬停位置
            #         hover_x = random.uniform(bounding_box['width'] * 0.2, bounding_box['width'] * 0.8)
            #         hover_y = random.uniform(bounding_box['height'] * 0.2, bounding_box['height'] * 0.8)
            #
            #         # 如果有之前的鼠标位置，使用贝塞尔曲线移动
            #         if last_mouse_x != 0 and last_mouse_y != 0:
            #             await simulate_human_mouse_movement(
            #                 page,
            #                 last_mouse_x, last_mouse_y,
            #                 bounding_box['x'] + hover_x, bounding_box['y'] + hover_y
            #             )
            #
            #         await canvas_locator.hover(position={'x': hover_x, 'y': hover_y}, timeout=5000)
            #         last_mouse_x, last_mouse_y = bounding_box['x'] + hover_x, bounding_box['y'] + hover_y
            #
            #         ## 偶尔模拟用户的误触点击
            #         # if random.random() < 0.03:  # 3%概率
            #         #     await page.mouse.click(last_mouse_x, last_mouse_y)
            #         #     await asyncio.sleep(random.uniform(0.2, 0.5))
            #
            #     else:
            #         logger.warning("无法获取Canvas边界框，可能已不可见。")
            #         break
            #
            # except Exception:
            #     logger.warning("在悬停时发生超时，判定为Canvas被遮挡或已滚动到底部，将结束滚动。")
            #     break

            # 生成更自然地滚动步长
            if random.random() < 0.2:  # 20%概率进行小步滚动
                step = random.randint(300, 500)
            # elif random.random() < 0.1:  # 10%概率进行大步滚动
            #     step = random.randint(800, 1200)
            else:
                step = random.randint(*scroll_step_range)

            # 记录滚动历史
            scroll_history.append(step)
            if len(scroll_history) > 10:  # 只保留最近10步
                scroll_history.pop(0)

            # 模拟更自然地滚动：有时分段滚动
            if random.random() < 0.25:  # 25%概率分段滚动
                mini_steps = random.randint(2, 4)
                mini_step = step // mini_steps
                for i in range(mini_steps):
                    await page.mouse.wheel(0, mini_step)
                    if i < mini_steps - 1:  # 不是最后一步时添加微小延迟
                        await asyncio.sleep(random.uniform(0.1, 0.4))
            else:
                await page.mouse.wheel(0, step)

            # 等待UI更新，添加随机性
            ui_wait_time = random.randint(150, 250)
            await page.wait_for_timeout(ui_wait_time)

            # 更新滚动信息
            _, new_scroll = await get_scroll_info_from_locators(frame)

            if new_scroll == current_scroll:
                logger.info("滚动位置不再变化，判定为已到达底部。")
                break

            current_scroll = new_scroll
            logger.info(f"滚动了 {step}px，当前位置: {current_scroll}/{total_height}")

        logger.info(f"智能滚动完成，最终位置: {current_scroll}px")

    except Exception as e:
        logger.error(f"智能滚动Canvas时发生错误: {e}")




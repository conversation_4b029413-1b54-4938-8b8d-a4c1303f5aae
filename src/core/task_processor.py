# -*- coding: utf-8 -*-
"""
任务处理器基类和具体实现
提供统一的任务处理接口和流程控制
"""
import asyncio
import time
from abc import ABC, abstractmethod
from typing import Any

from playwright.async_api import Page, Frame

from src.conf.config import CONFIG
from src.core.exceptions import TaskException, ErrorCodes
from src.flows.geek_fetch_flow import check_end_time, close_dialog, page_goto_and_check
from src.utils.logger import get_logger
from src.utils.tracing_manager import start_trace, stop_trace

logger = get_logger(__name__)


class BaseTaskProcessor(ABC):
    """任务处理器基类"""
    
    def __init__(self, page: Page):
        self.page = page
        self.task_id: str | None = None
        self.task_type: str | None = None
        self.account_name: str | None = None
        self.start_time: float | None = None
    
    @abstractmethod
    async def validate_params(self, payload: dict[str, Any]) -> None:
        """
        验证任务参数
        
        Args:
            payload: 任务负载数据
            
        Raises:
            TaskException: 参数验证失败时抛出
        """
        pass
    
    @abstractmethod
    async def execute_task(self, payload: dict[str, Any]) -> dict[str, Any]:
        """
        执行具体任务逻辑
        
        Args:
            payload: 任务负载数据
            
        Returns:
            Dict: 任务执行结果
            
        Raises:
            TaskException: 任务执行失败时抛出
        """
        pass
    
    async def process(self, task: dict[str, Any]) -> dict[str, Any]:
        """
        处理任务的主要流程
        
        Args:
            task: 完整的任务数据
            
        Returns:
            Dict: 处理结果
        """
        self.task_id = task.get("id", "unknown")
        self.task_type = task.get("action", "unknown")
        self.account_name = task.get("account_name")
        self.start_time = time.time()
        
        logger.info(f"开始处理任务: {self.task_id} ({self.task_type})")

        # 启动tracing记录
        await start_trace(self.page, self.task_type, self.account_name)

        # 验证参数
        payload = task.get("payload", {})
        await self.validate_params(payload)

        # 执行任务
        result = await self.execute_task(payload)

        # 记录成功
        processing_time = time.time() - self.start_time
        logger.info(f"任务处理完成: {self.task_id}, 耗时: {processing_time:.2f}秒")
        await stop_trace(self.page)

        # 发送任务成功通知
        # asyncio.create_task(send_task_notification(
        #     task_id=self.task_id,
        #     task_type=self.task_type,
        #     status="success",
        #     message="任务执行成功",
        #     duration=processing_time,
        #     account_name=self.account_name
        # ))

        return {
            "status": "success",
            "id": self.task_id,
            "data": result,
            "processing_time": processing_time
        }



class ResumeDetailProcessor(BaseTaskProcessor):
    """简历详情获取任务处理器"""
    
    async def validate_params(self, payload: dict[str, Any]) -> None:
        """验证简历详情获取任务的参数"""
        url = payload.get("url")
        if not url:
            raise TaskException(
                "缺少必要的url参数",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"missing_param": "url"}
            )
        
        if not isinstance(url, str) or not url.strip():
            raise TaskException(
                "url参数格式无效",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"invalid_param": "url", "value": url}
            )
    
    async def execute_task(self, payload: dict[str, Any]) -> dict[str, Any]:
        """执行简历详情获取任务"""
        url = payload["url"]
        logger.info(f"开始获取简历详情，URL: {url}")
        
        # 设置API响应监听
        resume_data = None
        
        async def handle_response(response):
            nonlocal resume_data
            if '/wapi/zpboss/h5/resume/share/h5/detail.json' in response.url:
                try:
                    response_data = await response.json()
                    if response_data.get("code") == 0:
                        resume_data = response_data.get("zpData")
                        logger.info(f"成功获取简历数据: {resume_data.get('geekName', '未知')}")
                    else:
                        logger.error(f"API返回错误: {response_data}")
                except Exception as e:
                    logger.error(f"解析响应数据失败: {e}")
        
        # 监听响应
        self.page.on("response", handle_response)
        
        try:
            # 访问URL
            await self.page.goto(url, wait_until="networkidle")
            
            # 等待API响应，最多等待10秒
            for _ in range(100):  # 10秒，每100ms检查一次
                if resume_data is not None:
                    break
                await asyncio.sleep(0.1)
            
            if resume_data:
                logger.info(f"简历详情获取成功: {resume_data.get('geekName', '未知')}")
                return resume_data
            else:
                raise TaskException(
                    "未能获取到简历数据",
                    ErrorCodes.DATA_EXTRACTION_FAILED,
                    {"url": url}
                )
                
        finally:
            # 移除监听器
            self.page.remove_listener("response", handle_response)


class LoginProcessor(BaseTaskProcessor):
    """登录任务处理器"""

    async def validate_params(self, payload: dict[str, Any]) -> None:
        """验证登录任务的参数"""
        account_name = payload.get("account_name")
        if not account_name:
            raise TaskException(
                "缺少必要的account_name参数",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"missing_param": "account_name"}
            )

    async def execute_task(self, payload: dict[str, Any]) -> dict[str, Any]:
        """执行登录任务"""
        from src.flows.login import login, wait_for_login_success
        import os

        account_name = payload["account_name"]
        bn_login_name = payload.get("bn_login_name", "")

        # 设置环境变量（保持原有逻辑）
        os.environ['account_name'] = account_name
        os.environ['bn_login_name'] = bn_login_name

        logger.info(f"开始登录账户: {account_name}")

        # 调用原有的登录逻辑
        manager, qr_code_url = await login(self.page, account_name)

        if qr_code_url:
            # 需要扫码登录
            logger.info(f"需要扫码登录，二维码URL: {qr_code_url}")

            # 在后台等待登录成功
            await wait_for_login_success(self.page, manager)

            return {
                "qrCodeUrl": qr_code_url,
                "login": True,
                "message": "登录成功"
            }
        else:
            # Cookie登录成功
            logger.info("通过Cookie直接登录成功")
            return {
                "qrCodeUrl": "",
                "login": True,
                "message": "Cookie登录成功"
            }


class JobFilterProcessor(BaseTaskProcessor):
    """职位过滤任务处理器（jobFilterTrigger）"""

    async def validate_params(self, payload: dict[str, Any]) -> None:
        """验证职位过滤任务的参数"""
        required_params = ["account_name", "jobId", "batchNo", "filterType"]
        missing_params = []

        for param in required_params:
            if param not in payload:
                missing_params.append(param)

        if missing_params:
            raise TaskException(
                f"缺少必要参数: {', '.join(missing_params)}",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"missing_params": missing_params}
            )

    async def execute_task(self, payload: dict[str, Any]) -> dict[str, str | dict | Any] | None:
        """执行职位过滤任务"""
        from src.flows.login import login
        from src.flows.geek_fetch_flow import fetch_recommended_geeks, close_dialog
        import os

        account_name = payload["account_name"]
        bn_login_name = payload.get("bn_login_name", "")
        job_id = payload["jobId"]
        job_name = payload.get("jobName", "")
        batch_no = payload["batchNo"]
        filter_type = payload["filterType"]
        end_time = payload.get("endTime", "")

        check_end_time(batch_no, end_time)

        # 设置环境变量（保持原有逻辑）
        os.environ['account_name'] = account_name
        os.environ['bn_login_name'] = bn_login_name

        logger.info(f"开始为账户 {account_name} 执行抓取任务...")

        # 登录
        manager = await login(self.page, account_name)
        if manager is None:
            raise TaskException(
                f"账户 {account_name} 登录失败",
                ErrorCodes.LOGIN_FAILED,
                {"account_name": account_name}
            )

        # 等待页面稳定
        try:
            logger.info("登录成功，等待页面网络稳定...")
            await self.page.wait_for_load_state('networkidle', timeout=3000)
            logger.info("页面已稳定，开始执行推荐牛人抓取任务。")
        except Exception:
            logger.warning("等待网络稳定超时，但继续执行。")

        # 关闭弹窗
        await close_dialog(self.page)

        # 获取职位详情信息
        job_detail = await self._get_job_detail(job_id)
        logger.info(f"职位详情: {job_detail}")

        # 执行抓取
        await fetch_recommended_geeks(
            job_id=job_id,
            job_name=job_name,
            filter_type=filter_type,
            batchNo=batch_no,
            page=self.page,
            endTime=end_time,
            account_name=account_name,
            job_detail=job_detail
        )

        return {
            "job_id": job_id,
            "job_name": job_name,
            "account_name": account_name,
            "job_detail": job_detail,
            "message": "抓取任务完成"
        }

    async def _get_job_detail(self, job_id: str) -> dict:
        """
        获取职位详情信息

        Args:
            job_id: 职位ID

        Returns:
            dict: 职位详情数据，如果获取失败返回空字典
        """
        try:
            logger.info(f"开始获取职位 {job_id} 的详情信息")

            # 1. 进入职位列表页面
            await self._navigate_to_job_list()

            # 2. 筛选开放中职位
            await self._filter_open_jobs()

            # 3. 点击任务jobId所在位置进入详情页面
            job_detail_opened = await self._open_job_detail(job_id)
            if not job_detail_opened:
                logger.error(f"无法打开职位 {job_id} 的详情页面")
                raise ValueError(f"无法打开职位 {job_id} 的详情页面")

            # 4. 获取职位详情并返回
            job_detail = await self._parse_job_detail(job_id)

            # 关闭详情弹窗
            await self._close_job_detail_dialog()

            logger.info(f"成功获取职位 {job_id} 的详情信息")
            return job_detail

        except Exception as e:
            logger.error(f"获取职位 {job_id} 详情失败: {e}", exc_info=True)
            raise ValueError(f"获取职位 {job_id} 详情失败: {e}")

    async def _navigate_to_job_list(self):
        from src.flows.geek_fetch_flow import close_dialog
        """进入职位列表页面"""
        try:
            logger.info("导航到职位列表页面")

            await self.page.goto("https://www.zhipin.com/web/chat/job/list")
            await self.page.wait_for_timeout(5000)  # 等待5秒

            await close_dialog(self.page)

            # 等待iframe加载
            await self.page.wait_for_selector("iframe[src*='/web/frame/job/list-new']", timeout=10000)

            # 处理可能的弹窗
            try:
                dialog_selector = "div[data-type='boss-dialog']"
                if await self.page.locator(dialog_selector).is_visible():
                    await self.page.locator(f"{dialog_selector} span.boss-dialog__button").click()
                    logger.info("关闭了提示弹窗")
            except Exception:
                logger.debug("没有发现提示弹窗")

            # 切换到iframe
            iframe = self.page.frame_locator("iframe[src*='/web/frame/job/list-new']")
            await iframe.locator(".job-list-content").wait_for(timeout=10000)

            logger.info("成功进入职位列表页面")

        except Exception as e:
            logger.error(f"导航到职位列表页面失败: {e}")
            raise

    async def _filter_open_jobs(self):
        """筛选开放中职位"""
        try:
            logger.info("筛选开放中职位")

            iframe = self.page.frame_locator("iframe[src*='/web/frame/job/list-new']")

            # 等待职位列表加载
            await iframe.locator(".job-list-content").wait_for(timeout=10000)

            # 这里可以添加筛选逻辑，比如点击"开放中"筛选按钮
            # 根据实际页面结构调整选择器
            try:
                filter_button = iframe.locator("text=开放中")
                if await filter_button.is_visible():
                    await filter_button.click()
                    await self.page.wait_for_timeout(2000)  # 等待筛选结果
                    logger.info("已筛选开放中职位")
            except Exception:
                logger.debug("未找到开放中筛选按钮，继续执行")

        except Exception as e:
            logger.error(f"筛选开放中职位失败: {e}")
            raise

    async def _open_job_detail(self, job_id: str) -> bool:
        """
        点击指定jobId的职位进入详情页面

        Args:
            job_id: 职位ID

        Returns:
            bool: 是否成功打开详情页面
        """
        try:
            logger.info(f"查找并点击职位 {job_id} 的详情按钮")

            iframe = self.page.frame_locator("iframe[src*='/web/frame/job/list-new']")

            # 构建职位选择器
            job_item_selector = f"li[data-id='{job_id}']"

            # 等待职位项出现
            await iframe.locator(job_item_selector).wait_for(timeout=10000)

            # 检查职位状态是否为"开放中"
            status_selector = f"{job_item_selector} .status-top"
            status_element = iframe.locator(status_selector)

            if await status_element.is_visible():
                status_text = await status_element.text_content()
                logger.debug(f"职位状态: {status_text}")

                if "开放中" not in status_text:
                    logger.error(f"职位状态不是开放中: {status_text}")
                    return False

            # 点击操作按钮
            operate_button_selector = f"{job_item_selector} .job-operate-wrapper.opreat-btn"
            operate_button = iframe.locator(operate_button_selector)

            # 滚动到元素可见位置
            await operate_button.scroll_into_view_if_needed()

            # 尝试点击操作按钮
            try:
                await operate_button.click()
            except Exception:
                # 如果普通点击失败，使用JavaScript点击
                await operate_button.evaluate("element => element.click()")

            # 点击预览按钮
            preview_button_selector = f"{job_item_selector} .job-operate-wrapper.opreat-btn li:first-child"
            preview_button = iframe.locator(preview_button_selector)

            await preview_button.scroll_into_view_if_needed()

            try:
                await preview_button.click()
            except Exception:
                await preview_button.evaluate("element => element.click()")

            # 切换回主页面等待弹窗出现
            dialog_selector = ".dialog-wrap.active .job-require-box"
            await self.page.locator(dialog_selector).wait_for(timeout=10000)

            logger.debug(f"成功打开职位 {job_id} 的详情页面")
            return True

        except Exception as e:
            logger.error(f"打开职位 {job_id} 详情页面失败: {e}")
            return False

    async def _parse_job_detail(self, job_id: str) -> dict:
        """
        解析职位详情数据

        Args:
            job_id: 职位ID

        Returns:
            dict: 解析后的职位详情数据
        """
        try:
            logger.info(f"开始解析职位 {job_id} 的详情数据")

            # 等待详情弹窗完全加载
            dialog_base = ".dialog-wrap.active .boss-dialog__body"
            await self.page.locator(dialog_base).wait_for(timeout=10000)

            job_detail = {"jobId": job_id}

            # 解析基本信息
            base_info_selector = f"{dialog_base} .base-info-box .item-info"

            try:
                # 职位类别 (第2个item-info)
                job_kind_element = self.page.locator(f"{base_info_selector}:nth-child(3) .content")
                if await job_kind_element.is_visible():
                    job_detail["jobkind"] = await job_kind_element.text_content()

                # 职位名称 (第3个item-info)
                job_name_element = self.page.locator(f"{base_info_selector}:nth-child(4) .content")
                if await job_name_element.is_visible():
                    job_detail["jobName"] = await job_name_element.text_content()

                # 职位描述 (第4个item-info)
                job_desc_element = self.page.locator(f"{base_info_selector}:nth-child(5) .content")
                if await job_desc_element.is_visible():
                    job_detail["jobDescription"] = await job_desc_element.text_content()

                # 职位类型 (第5个item-info)
                job_type_element = self.page.locator(f"{base_info_selector}:nth-child(6) .content")
                if await job_type_element.is_visible():
                    job_detail["jobType"] = await job_type_element.text_content()

                # 工作地点 (第6个item-info)
                job_location_element = self.page.locator(f"{base_info_selector}:nth-child(7) .content")
                if await job_location_element.is_visible():
                    job_detail["jobLocation"] = await job_location_element.text_content()

            except Exception as e:
                logger.warning(f"解析基本信息时出错: {e}")
                raise ValueError(f"解析基本信息时出错: {e}")

            # 解析职位要求信息
            require_info_selector = f"{dialog_base} .job-require-box .item-info"

            try:
                # 经验和学历要求 (第1个item-info)
                exp_edu_element = self.page.locator(f"{require_info_selector}:nth-child(2) .content")
                if await exp_edu_element.is_visible():
                    exp_edu_text = await exp_edu_element.text_content()
                    logger.info(f"经验学历要求: {exp_edu_text}")

                    # 分割经验和学历
                    if "·" in exp_edu_text:
                        parts = exp_edu_text.split("·")
                        job_detail["jobExperience"] = parts[0].strip()
                        job_detail["jobEducation"] = parts[1].strip() if len(parts) > 1 else ""
                    else:
                        job_detail["jobExperience"] = exp_edu_text.strip()
                        job_detail["jobEducation"] = ""

                # 薪资信息 (第2个item-info)
                salary_element = self.page.locator(f"{require_info_selector}:nth-child(3) .content")
                if await salary_element.is_visible():
                    salary_text = await salary_element.text_content()
                    logger.info(f"薪资信息: {salary_text}")

                    # 解析薪资范围和月数
                    if "-" in salary_text:
                        salary_parts = salary_text.split("-")
                        job_detail["jobMinMonthlySalary"] = salary_parts[0].strip()

                        if len(salary_parts) > 1:
                            max_salary_and_months = salary_parts[1].strip()
                            # 分离最高薪资和月数
                            max_parts = max_salary_and_months.split()
                            job_detail["jobMaxMonthlySalary"] = max_parts[0] if max_parts else ""
                            job_detail["jobPayrollMonths"] = max_parts[1] if len(max_parts) > 1 else ""
                    else:
                        job_detail["jobMinMonthlySalary"] = salary_text.strip()
                        job_detail["jobMaxMonthlySalary"] = ""
                        job_detail["jobPayrollMonths"] = ""

                # 技能关键词 (第3个item-info)
                keywords_element = self.page.locator(f"{require_info_selector}:nth-child(4) .content span")
                keywords = []
                keyword_count = await keywords_element.count()
                for i in range(keyword_count):
                    keyword_text = await keywords_element.nth(i).text_content()
                    if keyword_text:
                        keywords.append(keyword_text.strip())

                job_detail["jobKeywords"] = keywords

            except Exception as e:
                logger.warning(f"解析职位要求信息时出错: {e}")

            logger.info(f"成功解析职位 {job_id} 的详情数据: {job_detail}")
            return job_detail

        except Exception as e:
            logger.error(f"解析职位 {job_id} 详情数据失败: {e}")
            return {"jobId": job_id}

    async def _close_job_detail_dialog(self):
        """关闭职位详情弹窗"""
        try:
            # 查找并点击关闭按钮
            close_selectors = [
                ".dialog-wrap.active .boss-dialog__close",
                ".dialog-wrap.active .close-btn",
                ".dialog-wrap.active [class*='close']"
            ]

            for selector in close_selectors:
                try:
                    close_button = self.page.locator(selector)
                    if await close_button.is_visible():
                        await close_button.click()
                        logger.info("成功关闭职位详情弹窗")
                        return
                except Exception:
                    continue

            # 如果没有找到关闭按钮，尝试按ESC键
            await self.page.keyboard.press("Escape")
            logger.debug("通过ESC键关闭职位详情弹窗")

        except Exception as e:
            logger.warning(f"关闭职位详情弹窗失败: {e}")
            # 不抛出异常，因为这不是关键操作


# 职位相关处理器类
class JobListProcessor(BaseTaskProcessor):
    """职位列表获取处理器"""

    async def validate_params(self, payload: dict[str, Any]) -> None:
        """验证职位列表获取参数"""
        logger.info("开始验证职位列表获取参数......")

    async def execute_task(self, payload: dict[str, Any]) -> dict[str, Any]:
        """执行职位列表获取任务"""
        logger.info(f"开始获取职位列表")
        # 设置API响应监听
        job_list_data = None

        async def handle_response(response):
            nonlocal job_list_data
            if '/wapi/zpjob/job/data/list' in response.url:
                try:
                    response_data = await response.json()

                    jobList = []
                    if response_data.get("code") == 0:
                        pageNum = response_data["zpData"]["page"]
                        totalSize = response_data["zpData"]["totalSize"]
                        records = response_data["zpData"]["data"]
                        for job in records:
                            tmp = {"id": job["encryptId"],
                                   "name": job["jobName"],
                                   "locationName": job["locationName"],
                                   "experienceName": job["experienceName"],
                                   "education": job["degreeName"],
                                   "monthlySalary": job["salaryDesc"],
                                   "lowSalary": job["lowSalary"],
                                   "highSalary": job["highSalary"],
                                   "jobStatus": job["jobStatus"],
                                   "jobAuditStatus": job["jobAuditStatus"],
                                   "jobTypeName": job["jobTypeName"],
                                   "remainDays": job["remainDays"],
                                   "location": job["location"],
                                   "position": job["position"],
                                   "experience": job["experience"],
                                   "degree": job["degree"],
                                   "skillRequire": job["skillRequire"]}
                            jobList.append(tmp)
                        data = {"pageNum": pageNum, "total": totalSize, "records": jobList, "status": 0}
                        now = int(time.time() * 1000)
                    job_list_data = {"timestamp": str(now), "code": 0, "message": "操作成功", "result": data}
                    logger.info(f"resp:{job_list_data}")
                except Exception as e:
                    logger.error(f"解析响应数据失败: {e}")

        # 监听响应
        self.page.on("response", handle_response)

        try:
            # 访问URL
            await self.page.goto(CONFIG.BossZhiPin.API['job_list'], wait_until="networkidle")

            # 等待API响应，最多等待10秒
            for _ in range(100):  # 10秒，每100ms检查一次
                if job_list_data is not None:
                    break
                await asyncio.sleep(0.1)

            if job_list_data:
                logger.info(f"职位列表获取成功: {job_list_data.get('result', '未知')}")
                return job_list_data
            else:
                raise TaskException(
                    "未能获取到职位列表数据",
                    ErrorCodes.DATA_EXTRACTION_FAILED,
                    {"url": CONFIG.BossZhiPin.API['job_list']}
                )

        finally:
            # 移除监听器
            self.page.remove_listener("response", handle_response)


class WeeklyReportProcessor(BaseTaskProcessor):
    """周报数据获取任务处理器"""

    async def validate_params(self, payload: dict[str, Any]) -> None:
        """验证周报数据获取任务的参数"""
        pass


    async def execute_task(self, payload: dict[str, Any]) -> dict[str, Any]:
        """执行周报数据获取任务"""
        from datetime import date, timedelta
        url = CONFIG.BossZhiPin.API['data_recruit']
        logger.info(f"开始获取周报数据，URL: {url}")
        date_time = payload["date"]  # yyyyMMdd

        # 设置API响应监听
        recruit_data = None

        async def handle_response(response):
            nonlocal recruit_data
            if '/wapi/zpboss/h5/weeklyReportV3/recruitDataCenter/get.json' in response.url:
                try:
                    response_data = await response.json()
                    if response_data.get("code") == 0:
                        recruit_data = response_data.get("zpData")
                        logger.info(f"成功获取周报数据: {recruit_data}")
                    else:
                        logger.error(f"API返回错误: {response_data}")
                except Exception as e:
                    logger.error(f"解析响应数据失败: {e}")

        # 监听响应
        self.page.on("response", handle_response)

        try:
            await page_goto_and_check(url,self.page)
            await asyncio.sleep(1.5)
            if date_time:
                logger.info(f"开始获取周报数据，日期: {date_time}")
                # 目标日期（例：昨天）
                target_date = date.today() - timedelta(days=1)
                target_text = f"回查{target_date.strftime('%Y-%m-%d')}"

                await close_dialog(self.page)

                frame_handle = await self.page.locator("iframe[src*='/web/frame/report/data-center']").element_handle()
                iframe: Frame = await frame_handle.content_frame()
                # 精确定位到“今日数据”下拉框并点击
                dropdown = iframe.locator(".select-wrap .ui-select-inner:has-text('今日数据')")
                await dropdown.click()
                await asyncio.sleep(0.5)

                # 等待对应的“回查YYYY-MM-DD”选项出现并点击
                option = iframe.locator(f".ui-select-item.option-item:has-text('{target_text}')")
                await option.wait_for(state="visible", timeout=5000)
                await option.click()
            await asyncio.sleep(1.5)
            # 等待API响应，最多等待10秒
            for _ in range(100):  # 10秒，每100ms检查一次
                if recruit_data is not None:
                    break
                await asyncio.sleep(0.1)

            if recruit_data:
                logger.info(f"周报数据获取成功: {recruit_data}")
                return recruit_data
            else:
                raise TaskException(
                    "未能获取到周报数据",
                    ErrorCodes.DATA_EXTRACTION_FAILED,
                    {"url": url}
                )
        except Exception as e:
            logger.error(f"获取周报数据失败: {e}")
            raise ValueError(f"获取周报数据失败: {e}")
        finally:
            # 移除监听器
            self.page.remove_listener("response", handle_response)


# 任务处理器注册表
TASK_PROCESSORS = {
    "get_resume_detail": ResumeDetailProcessor,
    "login": LoginProcessor,
    "jobFilterTrigger": JobFilterProcessor,
    "get_job_list": JobListProcessor,
    "get_weekly_report": WeeklyReportProcessor,
}


def get_task_processor(action: str, page: Page) -> BaseTaskProcessor:
    """
    根据任务类型获取对应的处理器
    
    Args:
        action: 任务类型
        page: Playwright页面对象
        
    Returns:
        BaseTaskProcessor: 任务处理器实例
        
    Raises:
        TaskException: 不支持的任务类型
    """
    processor_class = TASK_PROCESSORS.get(action)
    if not processor_class:
        raise TaskException(
            f"不支持的任务类型: {action}",
            ErrorCodes.TASK_INVALID_PARAMS,
            {"action": action, "supported_actions": list(TASK_PROCESSORS.keys())}
        )
    
    return processor_class(page)

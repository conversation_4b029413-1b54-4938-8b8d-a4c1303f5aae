"""
任务处理器模块
负责处理具体的任务逻辑和异常处理
"""
import asyncio
import json
import time
import traceback
from datetime import datetime

import redis.asyncio as aioredis
from playwright.async_api import Page

from src.conf.config import CONFIG
from src.core.exceptions import ErrorCodes
from src.core.task_processor import get_task_processor
from src.monitors.heartbeat_monitor import update_worker_status, stop_heartbeat
from src.utils.cache_manager import cache_manager
from src.utils.logger import get_logger
from src.utils.mailer import send_monitoring_alert
from src.utils.tracing_manager import start_trace, stop_trace

logger = get_logger(__name__)

# 全局变量
is_task_running = False
current_task_info = None  # 当前执行的任务信息


async def handle_task(page: Page, task: dict, redis: aioredis.Redis):
    global is_task_running, current_task_info
    action = task.get("action")
    task_id = task.get("id", "N/A")
    task_type = action
    account_name = task.get("account_name") or task.get("payload", {}).get("account_name")
    start_time = time.time()

    # 检查是否有任务正在运行
    if is_task_running:
        error_result = {
            "status": "error",
            "id": task_id,
            "message": "另一个任务正在运行，请稍后再试",
            "error_code": ErrorCodes.TASK_CONCURRENT_CONFLICT
        }
        task_result_channel = task.get("result_channel")
        if task_result_channel:
            await redis.publish(task_result_channel, json.dumps(error_result))
        return error_result

    # 设置任务运行状态和当前任务信息
    is_task_running = True
    current_task_info = task.copy()  # 保存当前任务信息供心跳使用

    await update_worker_status(redis, "processing_task", task_info=task)

    logger.info(f"开始处理任务: {task_id} ({task_type})")

    # 启动tracing记录
    await start_trace(page, task_type, account_name)

    if page:
        current_url = page.url
        print(f"当前页面的URL是: {current_url}")
    else:
        raise Exception("无法将页面重置到初始状态，任务中止。")

    processor = get_task_processor(action, page)
    result = await processor.process(task)

    processing_time = time.time() - start_time
    logger.info(f"任务 {task_id} 处理完成，状态: {result.get('status', 'unknown')}, 耗时: {processing_time:.2f}秒")

    await stop_trace(page)

    # 如果任务成功，确保结果包含处理时间
    if result.get("status") == "success" and "processing_time" not in result:
        result["processing_time"] = processing_time
    is_task_running = False
    return result


async def _enter_management_mode(redis: aioredis.Redis, playwright_instance, page, initialize_browser):
    """
    进入管理模式，等待管理员指令处理异常情况

    Args:
        redis: Redis连接
        playwright_instance: Playwright实例
        page: 当前页面对象
        initialize_browser: 浏览器初始化函数
    """
    in_management_mode = True
    while in_management_mode:
        command = await pause_and_wait_for_admin_command(redis)

        if command == "shutdown":
            logger.info("[管理指令] 执行关闭...")
            await stop_heartbeat()  # 停止心跳任务
            await playwright_instance.stop()  # 确保 playwright 停止
            return "shutdown"  # 返回特殊值表示需要退出主循环

        elif command == "resume":
            logger.info("[管理指令] 恢复运行...")
            await update_worker_status(redis, "idle")
            in_management_mode = False  # 跳出管理模式，恢复任务循环

        elif command == "restart":
            logger.info("[管理指令] 执行浏览器重启...")
            # 关闭现有浏览器
            if page and page.context.browser.is_connected():
                await page.context.browser.close()
            # 重新初始化浏览器和页面
            new_page = await initialize_browser()
            if not new_page:
                logger.error("重启浏览器失败，程序退出。")
                await stop_heartbeat()  # 停止心跳任务
                await playwright_instance.stop()
                return "shutdown"
            await update_worker_status(redis, "idle")
            in_management_mode = False  # 跳出管理模式，恢复任务循环
            return new_page  # 返回新的页面对象

        elif command == "status":
            logger.info("[管理指令] 查询状态...")
            # 发送当前状态信息
            status_info = f"""
当前Worker状态: 管理模式 (等待指令)
用户ID: {CONFIG.worker_ip}
时间: {datetime.now().isoformat()}
可用指令: shutdown, resume, restart, status
"""
            await send_monitoring_alert(
                title=f"SRA Worker 状态查询 - {CONFIG.worker_ip}",
                error_details=status_info,
                page=page,
                severity="info"
            )
        else:
            logger.warning(f"[管理指令] 未知指令: {command}")

    return None  # 正常恢复


async def _get_task_error_details(e, account_name, page, is_task=True):
    url = page.url if page else "未知"
    if is_task:
        return f"""
SRA Worker ({account_name}) 在执行任务时遇到已知异常并已暂停，需要人工干预。

时间: {datetime.now().isoformat()}
异常类型: TaskException
错误代码: {getattr(e, 'error_code', None)}
错误信息: {getattr(e, 'message', str(e))}

最后访问的URL: {url}

异常详情:
{getattr(e, 'details', '')}

详细堆栈跟踪:
{traceback.format_exc()}"""
    else:
        login_expired = False
        if page and 'https://www.zhipin.com/web/user/?ka=' in url:
            login_expired = True
        return f"""
SRA Worker ({account_name}) 在执行任务时遇到严重未知异常并已暂停，需要人工干预。

时间: {datetime.now().isoformat()}
错误类型: {type(e).__name__}
错误信息: {str(e)}
登录状态: {'已失效' if login_expired else '正常'}

最后访问的URL: {url}

详细堆栈跟踪:
{traceback.format_exc()}"""



# --- 管理指令等待循环 ---
async def pause_and_wait_for_admin_command(redis: aioredis.Redis):
    """
    阻塞等待管理指令队列，收到指令后返回。
    支持指令：shutdown、resume、restart、status等。
    """
    queue_name = f"{CONFIG.Redis.ADMIN_QUEUE_PREFIX}{CONFIG.worker_ip}"
    print(f"\n{'=' * 50}\n[管理模式] 阻塞等待管理员指令（队列：{queue_name}）...")

    while True:
        result = await redis.blpop(queue_name, timeout=0)  # 阻塞直到有指令
        if result:
            _, raw_cmd = result
            try:
                # 兼容json字符串
                cmd_obj = json.loads(raw_cmd)
                command = cmd_obj.get("command") or cmd_obj.get("cmd") or raw_cmd
            except Exception:
                command = raw_cmd
            print(f"[管理指令] 收到指令: {command}")
            return command  # 返回指令给主循环
        await asyncio.sleep(0.1)  # 理论上不会走到这里，仅保险


class TaskHandlerManager:
    """任务处理器管理器"""
    
    def __init__(self):
        self.is_task_running = False
        self.current_task_info = None
    
    async def handle_task(self, page: Page, task: dict, redis: aioredis.Redis):
        """处理任务"""
        return await handle_task(page, task, redis)

    async def handle_exception_and_enter_management_mode(self, e: Exception, task: dict, page: Page, redis: aioredis.Redis, playwright_instance, initialize_browser, is_task_exception: bool = True):
        """
        处理异常并进入管理模式

        Args:
            e: 异常对象
            task: 任务数据
            page: Playwright页面对象
            redis: Redis连接
            playwright_instance: Playwright实例
            initialize_browser: 浏览器初始化函数
            is_task_exception: 是否为任务异常
        """
        # 处理异常
        import os
        account_name = os.environ.get('account_name')
        error_details = await _get_task_error_details(e, account_name, page, is_task=is_task_exception)
        severity = "error" if is_task_exception else "critical"
        title = f"SRA Worker 任务异常 - {CONFIG.worker_ip}" if is_task_exception else f"SRA Worker 严重异常 - {CONFIG.worker_ip}"
        # 发送监控告警
        await send_monitoring_alert(
            title=title,
            error_details=error_details,
            page=page,
            severity=severity
        )
        await update_worker_status(redis, "paused_on_error")

        cache_manager.set_precheck_flag("0")

        logger.info(("任务异常" if is_task_exception else "严重异常") + "，进入管理模式，等待管理员指令...")
        management_result = await _enter_management_mode(redis, playwright_instance, page, initialize_browser)
        if management_result == "shutdown":
            return None, "shutdown", page
        elif isinstance(management_result, Page):
            return None, "restart", management_result
        # 构造统一的 result
        result = {
            "status": "error",
            "id": task.get("id") if task else None,
            "message": getattr(e, "message", str(e)),
            "error_code": getattr(e, "error_code", None)
        }
        return result, None, page
    
    def get_status(self) -> dict:
        """获取处理器状态"""
        return {
            "is_task_running": is_task_running,
            "current_task_info": current_task_info
        }



# 全局任务处理器管理器实例
task_handler_manager = TaskHandlerManager()

# -*- coding: utf-8 -*-
"""
自定义异常类
统一项目中的异常处理
"""


class SRABaseException(Exception):
    """SRA项目基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> dict:
        """转换为字典格式，便于序列化"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class BrowserException(SRABaseException):
    """浏览器相关异常"""
    pass


class TaskException(SRABaseException):
    """任务处理相关异常"""
    pass


class LoginException(SRABaseException):
    """登录相关异常"""
    pass


class DataExtractionException(SRABaseException):
    """数据提取相关异常"""
    pass


class ValidationException(SRABaseException):
    """验证相关异常"""
    pass


class NetworkException(SRABaseException):
    """网络请求相关异常"""
    pass


class ConfigurationException(SRABaseException):
    """配置相关异常"""
    pass


# 预定义的错误代码常量
class ErrorCodes:
    """错误代码常量"""
    
    # 浏览器相关
    BROWSER_INIT_FAILED = "BROWSER_INIT_FAILED"
    BROWSER_NAVIGATION_FAILED = "BROWSER_NAVIGATION_FAILED"
    BROWSER_ELEMENT_NOT_FOUND = "BROWSER_ELEMENT_NOT_FOUND"
    BROWSER_ERROR = "BROWSER_ERROR"  # 新增：通用浏览器错误
    
    # 任务相关
    TASK_INVALID_PARAMS = "TASK_INVALID_PARAMS"
    TASK_EXECUTION_FAILED = "TASK_EXECUTION_FAILED"
    TASK_TIMEOUT = "TASK_TIMEOUT"
    TASK_CONCURRENT_CONFLICT = "TASK_CONCURRENT_CONFLICT"
    TASK_RESOURCE_LEAK = "TASK_RESOURCE_LEAK"
    TASK_DEPENDENCY_FAILED = "TASK_DEPENDENCY_FAILED"
    
    # 登录相关
    LOGIN_FAILED = "LOGIN_FAILED"
    LOGIN_TIMEOUT = "LOGIN_TIMEOUT"
    LOGIN_COOKIE_INVALID = "LOGIN_COOKIE_INVALID"
    LOGIN_REQUIRED = "LOGIN_REQUIRED"  # 新增：需要重新登录
    
    # 数据提取相关
    DATA_EXTRACTION_FAILED = "DATA_EXTRACTION_FAILED"
    DATA_PARSING_FAILED = "DATA_PARSING_FAILED"
    DATA_VALIDATION_FAILED = "DATA_VALIDATION_FAILED"
    
    # 网络相关
    NETWORK_REQUEST_FAILED = "NETWORK_REQUEST_FAILED"
    NETWORK_TIMEOUT = "NETWORK_TIMEOUT"
    NETWORK_CONNECTION_ERROR = "NETWORK_CONNECTION_ERROR"
    
    # 配置相关
    CONFIG_MISSING = "CONFIG_MISSING"
    CONFIG_INVALID = "CONFIG_INVALID"
    
    # 页面结构相关
    PAGE_STRUCTURE_CHANGED = "PAGE_STRUCTURE_CHANGED"  # 新增：页面结构发生变化
